      1@X   0D:\lib_copy\LMS-main20250704\LMS-main\.gitignore                           .D:\lib_copy\LMS-main20250704\back_end\.gitkeep                           hD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CompilerIdCXX\a.exe                           fD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CompilerIdC\a.exe                           HD:\lib_copy\LMS-main20250704\LMS-main\window\adminWindow\adminwindow.cpp                           CD:\lib_copy\LMS-main20250704\LMS-main\include\library\adminwindow.h                           GD:\lib_copy\LMS-main20250704\LMS-main\window\adminWindow\adminwindow.ui                           2D:\lib_copy\LMS-main20250704\LMS-main\src\Book.cpp                           <D:\lib_copy\LMS-main20250704\LMS-main\include\library\Book.h                           ND:\lib_copy\LMS-main20250704\LMS-main\window\bookEditDialog\bookeditdialog.cpp                           FD:\lib_copy\LMS-main20250704\LMS-main\include\library\bookeditdialog.h                           MD:\lib_copy\LMS-main20250704\LMS-main\window\bookEditDialog\bookeditdialog.ui                           RD:\lib_copy\LMS-main20250704\LMS-main\window\borrowInfoDialog\borrowinfodialog.cpp                           HD:\lib_copy\LMS-main20250704\LMS-main\include\library\borrowinfodialog.h                           QD:\lib_copy\LMS-main20250704\LMS-main\window\borrowInfoDialog\borrowinfodialog.ui                           hD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\build.make                           `D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\cmake.check_cache                           RD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeCache.txt                           iD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeCCompiler.cmake                           sD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CompilerIdC\CMakeCCompilerId.c                           eD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\CMakeConfigureLog.yaml                           kD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake                           yD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CompilerIdCXX\CMakeCXXCompilerId.cpp                           tD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeDetermineCompilerABI_C.bin                           vD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeDetermineCompilerABI_CXX.bin                           nD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\CMakeDirectoryInformation.cmake                           RD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeLists.txt                           <D:\lib_copy\LMS-main20250704\back_end\TaskAPI\CMakeLists.txt                           4D:\lib_copy\LMS-main20250704\LMS-main\CMakeLists.txt                           9D:\lib_copy\LMS-main20250704\LMS-main\CMakeLists.txt.user                           AD:\lib_copy\LMS-main20250704\LMS-main\CMakeLists.txt.user.c9042d4                           7D:\lib_copy\LMS-main20250704\LMS-main\CMakePresets.json                           jD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeRCCompiler.cmake                           fD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\4.0.3\CMakeSystem.cmake                           ;D:\lib_copy\LMS-main20250704\LMS-main\CMakeUserPresets.json                           <D:\lib_copy\LMS-main20250704\.vs\CMakeWorkspaceSettings.json                           oD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\cmake_clean.cmake                           WD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\cmake_install.cmake                           rD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\compiler_depend.make                           pD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\compiler_depend.ts                           >D:\lib_copy\LMS-main20250704\LMS-main\util\CompressionUtil.cpp                           GD:\lib_copy\LMS-main20250704\LMS-main\include\library\CompressionUtil.h                           iD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\depend.make                           nD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\DependInfo.cmake                           ;D:\lib_copy\LMS-main20250704\LMS-main\resources\favicon.png                           hD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\flags.make                           ZD:\lib_copy\LMS-main20250704\LMS-main\window\forgotpasswordwindow\forgotpasswordwindow.cpp                           LD:\lib_copy\LMS-main20250704\LMS-main\include\library\forgotpasswordwindow.h                           YD:\lib_copy\LMS-main20250704\LMS-main\window\forgotpasswordwindow\forgotpasswordwindow.ui                           8D:\lib_copy\LMS-main20250704\LMS-main\resources\icon.qrc                           nD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\includes_CXX.rsp                           bD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\InstallScripts.json                           8D:\lib_copy\LMS-main20250704\LMS-main\.vs\launch.vs.json                           5D:\lib_copy\LMS-main20250704\LMS-main\src\Library.cpp                           ?D:\lib_copy\LMS-main20250704\LMS-main\include\library\Library.h                           fD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\link.txt                           jD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\linkLibs.rsp                           RD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\lms_server.exe                           TD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\lms_server_1.exe                           HD:\lib_copy\LMS-main20250704\LMS-main\window\loginWindow\loginwindow.cpp                           CD:\lib_copy\LMS-main20250704\LMS-main\include\library\loginwindow.h                           GD:\lib_copy\LMS-main20250704\LMS-main\window\loginWindow\loginwindow.ui                           LD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\main.cpp                           2D:\lib_copy\LMS-main20250704\LMS-main\src\main.cpp                           jD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\main.cpp.obj                           lD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\main.cpp.obj.d                           FD:\lib_copy\LMS-main20250704\LMS-main\window\mainWindow\mainwindow.cpp                           BD:\lib_copy\LMS-main20250704\LMS-main\include\library\mainwindow.h                           ED:\lib_copy\LMS-main20250704\LMS-main\window\mainWindow\mainwindow.ui                           LD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\Makefile                           ]D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\Makefile.cmake                           XD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\Makefile2                           gD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\objects.a                           jD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\objects1.rsp                           kD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\progress.make                           ]D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\progress.marks                           5D:\lib_copy\LMS-main20250704\.vs\ProjectSettings.json                           >D:\lib_copy\LMS-main20250704\LMS-main\.vs\ProjectSettings.json                           /D:\lib_copy\LMS-main20250704\LMS-main\README.md                           ND:\lib_copy\LMS-main20250704\LMS-main\window\RegisterWindow\RegisterWindow.cpp                           FD:\lib_copy\LMS-main20250704\LMS-main\include\library\RegisterWindow.h                           MD:\lib_copy\LMS-main20250704\LMS-main\window\RegisterWindow\RegisterWindow.ui                           dD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\TargetDirectories.txt                           2D:\lib_copy\LMS-main20250704\LMS-main\src\User.cpp                           <D:\lib_copy\LMS-main20250704\LMS-main\include\library\User.h                           MD:\lib_copy\LMS-main20250704\back_end\library-management-system-API\user.json                           6D:\lib_copy\LMS-main20250704\.vs\VSWorkspaceState.json                           ?D:\lib_copy\LMS-main20250704\LMS-main\.vs\VSWorkspaceState.json                           