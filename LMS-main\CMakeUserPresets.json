{"version": 3, "configurePresets": [{"name": "Qt-Debug", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug", "CMAKE_CXX_FLAGS": "-DQT_QML_DEBUG"}, "environment": {"QML_DEBUG_ARGS": "-qmljsdebugger=file:{43efdad1-0a24-43bb-8476-ecdafc8b236d},block"}}, {"name": "Qt-Release", "inherits": "Qt-<PERSON><PERSON><PERSON>", "binaryDir": "${sourceDir}/out/build/release", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"hidden": true, "name": "Qt-<PERSON><PERSON><PERSON>", "inherits": "6.9.1_msvc2022_64", "vendor": {"qt-project.org/Default": {"checksum": "Bb7AR9VKZ6X2fuMIw7hafNcnaMA="}}}, {"hidden": true, "name": "6.9.1_msvc2022_64", "inherits": "Qt", "environment": {"QTDIR": "C:/Qt/6.9.1/msvc2022_64"}, "architecture": {"strategy": "external", "value": "x64"}, "generator": "Ninja", "vendor": {"qt-project.org/Version": {"checksum": "03fD+PbilhUO7cvUkGKIxUl7gkU="}}}], "vendor": {"qt-project.org/Presets": {"checksum": "vMSunOncG/uyUtJnEjw807GsNCI="}}}