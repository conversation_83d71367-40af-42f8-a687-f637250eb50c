#pragma once

#include <QDialog>
#include "Library.h"
#include "loginWindow.h" 
#include <QVBoxLayout>

#include <QNetworkAccessManager>
#include <QNetworkReply>

class QLineEdit;
class QPushButton;
class LoginWindow;

class RegisterWindow : public QDialog {
    Q_OBJECT

public:
    explicit RegisterWindow(Library* library, LoginWindow* loginWindow, QWidget* parent = nullptr);


    ~RegisterWindow();

private slots:
    void onRegisterClicked();
    void onExitClicked();

    void onNetworkReplyFinished(QNetworkReply* reply);  // 新增槽，处理网络响应

private:
    QLineEdit* usernameEdit;
    QLineEdit* passwordEdit;
    QLineEdit* answerEdit;

    QPushButton* registerButton;
    QPushButton* exitButton;
    LoginWindow* loginWindow;
    Library* library;
   
    QNetworkAccessManager* networkManager;  // 新增，网络管理器

    int generateUniqueId();  // 生成唯一的ID
};
