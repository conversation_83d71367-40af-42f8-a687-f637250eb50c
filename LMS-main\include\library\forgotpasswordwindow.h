#ifndef FORGOTPASSWORD<PERSON>NDOW_H
#define FORGOTPASSWORDWINDOW_H

//
// Created by <PERSON><PERSON><PERSON> on 25-6-30.
//
#pragma once
#include <QDialog>
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QLabel>
#include "Library.h"

class ForgotPasswordWindow : public QDialog {
    Q_OBJECT
public:
    explicit ForgotPasswordWindow(Library* library, QWidget* parent = nullptr);
    ~ForgotPasswordWindow() override;

private slots:
    void onVerifyButtonClicked();
    void onConfirmButtonClicked();
    void onExitButtonClicked();

private:
    void createWidgets();
    void setupLayout();
    void connectSignalsSlots();

    Library* library;
    User* currentUser;
    bool isVerified;

    // 界面组件
    QLabel* idLabel;
    QLineEdit* idEdit;
    QLabel* questionLabel;
    QLineEdit* answerEdit;
    QPushButton* verifyButton;
    QLabel* passwordLabel;
    QLineEdit* passwordEdit;
    QPushButton* confirmButton;
    QPushButton* exitButton;
};
#endif // FORGOTPASSWORDWINDOW_H
