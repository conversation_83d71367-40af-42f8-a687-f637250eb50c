#pragma once

#include <QWidget>
#include "Library.h"
#include <QVBoxLayout>

class QLineEdit;
class QPushButton;

class LoginWindow final : public QWidget {
    Q_OBJECT

public:
    explicit LoginWindow(Library* library, QWidget* parent = nullptr);

signals:
    void loginSuccessful(User* user);

private slots:
    void onLoginClicked();
    void onPasswordReturnPressed();
    void onForgotPasswordClicked();
    void onRegisterClicked();
private:
    QLineEdit* idEdit;
    QLineEdit* passwordEdit;
    QPushButton* loginButton;
    Library* library;
    QPushButton* forgotPasswordButton;
    QPushButton* registerButton;
};
