
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/tdm-gcc/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/4.0.3/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/tdm-gcc/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/4.0.3/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27"
      binary: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_c44e2/fast
        D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_c44e2.dir\\build.make CMakeFiles/cmTC_c44e2.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27'
        Building C object CMakeFiles/cmTC_c44e2.dir/CMakeCCompilerABI.c.obj
        D:\\tdm-gcc\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
        Using built-in specs.
        COLLECT_GCC=D:\\tdm-gcc\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1.exe -quiet -v -iprefix D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccieEDqa.s
        GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 68074fcaab9f6b1377b55f7cea05149b
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccieEDqa.s
        GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1
        COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../libexec/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../lib/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
        Linking C executable cmTC_c44e2.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_c44e2.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_c44e2.dir/objects.a
        D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_c44e2.dir/objects.a @CMakeFiles\\cmTC_c44e2.dir\\objects1.rsp
        D:\\tdm-gcc\\bin\\gcc.exe -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a -Wl,--no-whole-archive -o cmTC_c44e2.exe -Wl,--out-implib,libcmTC_c44e2.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=D:\\tdm-gcc\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../libexec/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../lib/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c44e2.exe' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYtotqi.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_c44e2.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a --no-whole-archive --out-implib libcmTC_c44e2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        collect2 version 10.3.0
        D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYtotqi.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_c44e2.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a --no-whole-archive --out-implib libcmTC_c44e2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        GNU ld (GNU Binutils) 2.36.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c44e2.exe' '-mtune=generic' '-march=x86-64'
        mingw32-make.exe[1]: Leaving directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include] ==> [D:/tdm-gcc/include]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include] ==> [D:/tdm-gcc/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include;D:/tdm-gcc/include;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed;D:/tdm-gcc/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_c44e2/fast]
        ignore line: [D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_c44e2.dir\\build.make CMakeFiles/cmTC_c44e2.dir/build]
        ignore line: [mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-4ajt27']
        ignore line: [Building C object CMakeFiles/cmTC_c44e2.dir/CMakeCCompilerABI.c.obj]
        ignore line: [D:\\tdm-gcc\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm-gcc\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1.exe -quiet -v -iprefix D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccieEDqa.s]
        ignore line: [GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 68074fcaab9f6b1377b55f7cea05149b]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccieEDqa.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1]
        ignore line: [COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../libexec/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_c44e2.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking C executable cmTC_c44e2.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_c44e2.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_c44e2.dir/objects.a]
        ignore line: [D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_c44e2.dir/objects.a @CMakeFiles\\cmTC_c44e2.dir\\objects1.rsp]
        ignore line: [D:\\tdm-gcc\\bin\\gcc.exe -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a -Wl --no-whole-archive -o cmTC_c44e2.exe -Wl --out-implib libcmTC_c44e2.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm-gcc\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../libexec/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_c44e2.exe' '-mtune=generic' '-march=x86-64']
        link line: [ D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYtotqi.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_c44e2.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a --no-whole-archive --out-implib libcmTC_c44e2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
          arg [D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYtotqi.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [--exclude-libs=libpthread.a] ==> ignore
          arg [--undefined=__xl_f] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_c44e2.exe] ==> ignore
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0]
          arg [-LD:/tdm-gcc/bin/../lib/gcc] ==> dir [D:/tdm-gcc/bin/../lib/gcc]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_c44e2.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_c44e2.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        ignore line: [collect2 version 10.3.0]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccYtotqi.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_c44e2.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_c44e2.dir/objects.a --no-whole-archive --out-implib libcmTC_c44e2.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        linker tool for 'C': D:/tdm-gcc/x86_64-w64-mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc] ==> [D:/tdm-gcc/lib/gcc]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> [D:/tdm-gcc/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> [D:/tdm-gcc/lib]
        implicit libs: [mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32]
        implicit objs: [D:/tdm-gcc/x86_64-w64-mingw32/lib/crt2.o;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o;D:/tdm-gcc/x86_64-w64-mingw32/lib/default-manifest.o;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        implicit dirs: [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0;D:/tdm-gcc/lib/gcc;D:/tdm-gcc/x86_64-w64-mingw32/lib;D:/tdm-gcc/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "D:/tdm-gcc/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.36.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs"
      binary: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_d3ca0/fast
        D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_d3ca0.dir\\build.make CMakeFiles/cmTC_d3ca0.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs'
        Building CXX object CMakeFiles/cmTC_d3ca0.dir/CMakeCXXCompilerABI.cpp.obj
        D:\\tdm-gcc\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Using built-in specs.
        COLLECT_GCC=D:\\tdm-gcc\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1plus.exe -quiet -v -iprefix D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8fbIlZ.s
        GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"
        ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 5e49fa89ed7bfe906173ae8e94e08de6
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8fbIlZ.s
        GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1
        COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../libexec/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../lib/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
        Linking CXX executable cmTC_d3ca0.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_d3ca0.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_d3ca0.dir/objects.a
        D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_d3ca0.dir/objects.a @CMakeFiles\\cmTC_d3ca0.dir\\objects1.rsp
        D:\\tdm-gcc\\bin\\g++.exe -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a -Wl,--no-whole-archive -o cmTC_d3ca0.exe -Wl,--out-implib,libcmTC_d3ca0.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=D:\\tdm-gcc\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../libexec/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm-gcc/bin/../lib/gcc/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d3ca0.exe' '-mtune=generic' '-march=x86-64'
         D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZiiGkR.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_d3ca0.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a --no-whole-archive --out-implib libcmTC_d3ca0.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        collect2 version 10.3.0
        D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZiiGkR.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_d3ca0.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a --no-whole-archive --out-implib libcmTC_d3ca0.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        GNU ld (GNU Binutils) 2.36.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d3ca0.exe' '-mtune=generic' '-march=x86-64'
        mingw32-make.exe[1]: Leaving directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
          add: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include] ==> [D:/tdm-gcc/include]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        collapse include dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include] ==> [D:/tdm-gcc/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include;D:/tdm-gcc/include;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed;D:/tdm-gcc/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs']
        ignore line: []
        ignore line: [Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_d3ca0/fast]
        ignore line: [D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_d3ca0.dir\\build.make CMakeFiles/cmTC_d3ca0.dir/build]
        ignore line: [mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-fm3ybs']
        ignore line: [Building CXX object CMakeFiles/cmTC_d3ca0.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [D:\\tdm-gcc\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj -c "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm-gcc\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1plus.exe -quiet -v -iprefix D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8fbIlZ.s]
        ignore line: [GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/tdm-gcc/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 5e49fa89ed7bfe906173ae8e94e08de6]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc8fbIlZ.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1]
        ignore line: [COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../libexec/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_d3ca0.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking CXX executable cmTC_d3ca0.exe]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_d3ca0.dir\\link.txt --verbose=1]
        ignore line: ["C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_d3ca0.dir/objects.a]
        ignore line: [D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_d3ca0.dir/objects.a @CMakeFiles\\cmTC_d3ca0.dir\\objects1.rsp]
        ignore line: [D:\\tdm-gcc\\bin\\g++.exe -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a -Wl --no-whole-archive -o cmTC_d3ca0.exe -Wl --out-implib libcmTC_d3ca0.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm-gcc\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COMPILER_PATH=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../libexec/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d3ca0.exe' '-mtune=generic' '-march=x86-64']
        link line: [ D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZiiGkR.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_d3ca0.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a --no-whole-archive --out-implib libcmTC_d3ca0.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
          arg [D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZiiGkR.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [--exclude-libs=libpthread.a] ==> ignore
          arg [--undefined=__xl_f] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_d3ca0.exe] ==> ignore
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0]
          arg [-LD:/tdm-gcc/bin/../lib/gcc] ==> dir [D:/tdm-gcc/bin/../lib/gcc]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_d3ca0.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_d3ca0.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-Bstatic] ==> search static
          arg [-lstdc++] ==> lib [SEARCH_STATIC:stdc++]
          arg [-Bdynamic] ==> search dynamic
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        ignore line: [collect2 version 10.3.0]
        ignore line: [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm-gcc/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccZiiGkR.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_d3ca0.exe D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm-gcc/bin/../lib/gcc -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_d3ca0.dir/objects.a --no-whole-archive --out-implib libcmTC_d3ca0.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        linker tool for 'CXX': D:/tdm-gcc/x86_64-w64-mingw32/bin/ld.exe
        search lib [SEARCH_STATIC:stdc++] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/libstdc++.a]
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc] ==> [D:/tdm-gcc/lib/gcc]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> [D:/tdm-gcc/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/tdm-gcc/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm-gcc/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> [D:/tdm-gcc/lib]
        implicit libs: [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/libstdc++.a;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32]
        implicit objs: [D:/tdm-gcc/x86_64-w64-mingw32/lib/crt2.o;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o;D:/tdm-gcc/x86_64-w64-mingw32/lib/default-manifest.o;D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        implicit dirs: [D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0;D:/tdm-gcc/lib/gcc;D:/tdm-gcc/x86_64-w64-mingw32/lib;D:/tdm-gcc/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "D:/tdm-gcc/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.36.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:18 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-314m71"
      binary: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-314m71"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-314m71'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_dbe91/fast
        D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_dbe91.dir\\build.make CMakeFiles/cmTC_dbe91.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-314m71'
        Building C object CMakeFiles/cmTC_dbe91.dir/src.c.obj
        D:\\tdm-gcc\\bin\\gcc.exe -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles\\cmTC_dbe91.dir\\src.c.obj -c D:\\course\\DevOps\\LMS-main20250704\\LMS-main\\out\\CMakeFiles\\CMakeScratch\\TryCompile-314m71\\src.c
        Linking C executable cmTC_dbe91.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_dbe91.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_dbe91.dir/objects.a
        D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_dbe91.dir/objects.a @CMakeFiles\\cmTC_dbe91.dir\\objects1.rsp
        D:\\tdm-gcc\\bin\\gcc.exe -Wl,--whole-archive CMakeFiles\\cmTC_dbe91.dir/objects.a -Wl,--no-whole-archive -o cmTC_dbe91.exe -Wl,--out-implib,libcmTC_dbe91.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_dbe91.dir\\linkLibs.rsp
        mingw32-make.exe[1]: Leaving directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-314m71'
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:18 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-zzd0v6"
      binary: "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-zzd0v6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-zzd0v6'
        
        Run Build Command(s): "C:/Program Files/CMake/bin/cmake.exe" -E env VERBOSE=1 D:/tdm-gcc/bin/mingw32-make.exe -f Makefile cmTC_70ff0/fast
        D:/tdm-gcc/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_70ff0.dir\\build.make CMakeFiles/cmTC_70ff0.dir/build
        mingw32-make.exe[1]: Entering directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-zzd0v6'
        Building CXX object CMakeFiles/cmTC_70ff0.dir/src.cxx.obj
        D:\\tdm-gcc\\bin\\g++.exe -DHAVE_STDATOMIC  -std=gnu++2a -o CMakeFiles\\cmTC_70ff0.dir\\src.cxx.obj -c D:\\course\\DevOps\\LMS-main20250704\\LMS-main\\out\\CMakeFiles\\CMakeScratch\\TryCompile-zzd0v6\\src.cxx
        Linking CXX executable cmTC_70ff0.exe
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E cmake_link_script CMakeFiles\\cmTC_70ff0.dir\\link.txt --verbose=1
        "C:\\Program Files\\CMake\\bin\\cmake.exe" -E rm -f CMakeFiles\\cmTC_70ff0.dir/objects.a
        D:\\tdm-gcc\\bin\\ar.exe qc CMakeFiles\\cmTC_70ff0.dir/objects.a @CMakeFiles\\cmTC_70ff0.dir\\objects1.rsp
        D:\\tdm-gcc\\bin\\g++.exe -Wl,--whole-archive CMakeFiles\\cmTC_70ff0.dir/objects.a -Wl,--no-whole-archive -o cmTC_70ff0.exe -Wl,--out-implib,libcmTC_70ff0.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_70ff0.dir\\linkLibs.rsp
        mingw32-make.exe[1]: Leaving directory 'D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/CMakeScratch/TryCompile-zzd0v6'
        
      exitCode: 0
...
