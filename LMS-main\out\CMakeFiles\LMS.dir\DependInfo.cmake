
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp" "CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj" "gcc" "CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/mocs_compilation.cpp" "CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj" "gcc" "CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/src/Book.cpp" "CMakeFiles/LMS.dir/src/Book.cpp.obj" "gcc" "CMakeFiles/LMS.dir/src/Book.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/src/Library.cpp" "CMakeFiles/LMS.dir/src/Library.cpp.obj" "gcc" "CMakeFiles/LMS.dir/src/Library.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/src/User.cpp" "CMakeFiles/LMS.dir/src/User.cpp.obj" "gcc" "CMakeFiles/LMS.dir/src/User.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/src/main.cpp" "CMakeFiles/LMS.dir/src/main.cpp.obj" "gcc" "CMakeFiles/LMS.dir/src/main.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/util/CompressionUtil.cpp" "CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj" "gcc" "CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/RegisterWindow/registerwindow.cpp" "CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/adminWindow/adminwindow.cpp" "CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/bookEditDialog/bookeditdialog.cpp" "CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/borrowInfoDialog/borrowinfodialog.cpp" "CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/forgotpasswordwindow/forgotpasswordwindow.cpp" "CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/loginWindow/loginwindow.cpp" "CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj.d"
  "D:/course/DevOps/LMS-main20250704/LMS-main/window/mainWindow/mainwindow.cpp" "CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj" "gcc" "CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
