# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\course\DevOps\LMS-main20250704\LMS-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\course\DevOps\LMS-main20250704\LMS-main\out

# Include any dependencies generated for this target.
include CMakeFiles/LMS.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/LMS.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/LMS.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/LMS.dir/flags.make

LMS_autogen/3YJK5W5UP7/qrc_icon.cpp: D:/course/DevOps/LMS-main20250704/LMS-main/resources/icon.qrc
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp: CMakeFiles/LMS_autogen.dir/AutoRcc_icon_3YJK5W5UP7_Info.json
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp: D:/course/DevOps/LMS-main20250704/LMS-main/resources/favicon.png
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp: D:/QT/6.9.1/msvc2022_64/bin/rcc.exe
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp: D:/QT/6.9.1/msvc2022_64/bin/rcc.exe
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic RCC for resources/icon.qrc"
	"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/LMS_autogen.dir/AutoRcc_icon_3YJK5W5UP7_Info.json 

CMakeFiles/LMS.dir/codegen:
.PHONY : CMakeFiles/LMS.dir/codegen

CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj: LMS_autogen/mocs_compilation.cpp
CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj -MF CMakeFiles\LMS.dir\LMS_autogen\mocs_compilation.cpp.obj.d -o CMakeFiles\LMS.dir\LMS_autogen\mocs_compilation.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\mocs_compilation.cpp

CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\mocs_compilation.cpp > CMakeFiles\LMS.dir\LMS_autogen\mocs_compilation.cpp.i

CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\mocs_compilation.cpp -o CMakeFiles\LMS.dir\LMS_autogen\mocs_compilation.cpp.s

CMakeFiles/LMS.dir/src/main.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/src/main.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/src/main.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/src/main.cpp
CMakeFiles/LMS.dir/src/main.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/LMS.dir/src/main.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/src/main.cpp.obj -MF CMakeFiles\LMS.dir\src\main.cpp.obj.d -o CMakeFiles\LMS.dir\src\main.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\src\main.cpp

CMakeFiles/LMS.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/src/main.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\src\main.cpp > CMakeFiles\LMS.dir\src\main.cpp.i

CMakeFiles/LMS.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/src/main.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\src\main.cpp -o CMakeFiles\LMS.dir\src\main.cpp.s

CMakeFiles/LMS.dir/src/Book.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/src/Book.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/src/Book.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/src/Book.cpp
CMakeFiles/LMS.dir/src/Book.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/LMS.dir/src/Book.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/src/Book.cpp.obj -MF CMakeFiles\LMS.dir\src\Book.cpp.obj.d -o CMakeFiles\LMS.dir\src\Book.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\src\Book.cpp

CMakeFiles/LMS.dir/src/Book.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/src/Book.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\src\Book.cpp > CMakeFiles\LMS.dir\src\Book.cpp.i

CMakeFiles/LMS.dir/src/Book.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/src/Book.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\src\Book.cpp -o CMakeFiles\LMS.dir\src\Book.cpp.s

CMakeFiles/LMS.dir/src/User.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/src/User.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/src/User.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/src/User.cpp
CMakeFiles/LMS.dir/src/User.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/LMS.dir/src/User.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/src/User.cpp.obj -MF CMakeFiles\LMS.dir\src\User.cpp.obj.d -o CMakeFiles\LMS.dir\src\User.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\src\User.cpp

CMakeFiles/LMS.dir/src/User.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/src/User.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\src\User.cpp > CMakeFiles\LMS.dir\src\User.cpp.i

CMakeFiles/LMS.dir/src/User.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/src/User.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\src\User.cpp -o CMakeFiles\LMS.dir\src\User.cpp.s

CMakeFiles/LMS.dir/src/Library.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/src/Library.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/src/Library.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/src/Library.cpp
CMakeFiles/LMS.dir/src/Library.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/LMS.dir/src/Library.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/src/Library.cpp.obj -MF CMakeFiles\LMS.dir\src\Library.cpp.obj.d -o CMakeFiles\LMS.dir\src\Library.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\src\Library.cpp

CMakeFiles/LMS.dir/src/Library.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/src/Library.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\src\Library.cpp > CMakeFiles\LMS.dir\src\Library.cpp.i

CMakeFiles/LMS.dir/src/Library.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/src/Library.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\src\Library.cpp -o CMakeFiles\LMS.dir\src\Library.cpp.s

CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/loginWindow/loginwindow.cpp
CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj -MF CMakeFiles\LMS.dir\window\loginWindow\loginwindow.cpp.obj.d -o CMakeFiles\LMS.dir\window\loginWindow\loginwindow.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\loginWindow\loginwindow.cpp

CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\loginWindow\loginwindow.cpp > CMakeFiles\LMS.dir\window\loginWindow\loginwindow.cpp.i

CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\loginWindow\loginwindow.cpp -o CMakeFiles\LMS.dir\window\loginWindow\loginwindow.cpp.s

CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/forgotpasswordwindow/forgotpasswordwindow.cpp
CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj -MF CMakeFiles\LMS.dir\window\forgotpasswordwindow\forgotpasswordwindow.cpp.obj.d -o CMakeFiles\LMS.dir\window\forgotpasswordwindow\forgotpasswordwindow.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\forgotpasswordwindow\forgotpasswordwindow.cpp

CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\forgotpasswordwindow\forgotpasswordwindow.cpp > CMakeFiles\LMS.dir\window\forgotpasswordwindow\forgotpasswordwindow.cpp.i

CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\forgotpasswordwindow\forgotpasswordwindow.cpp -o CMakeFiles\LMS.dir\window\forgotpasswordwindow\forgotpasswordwindow.cpp.s

CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/RegisterWindow/registerwindow.cpp
CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj -MF CMakeFiles\LMS.dir\window\RegisterWindow\registerwindow.cpp.obj.d -o CMakeFiles\LMS.dir\window\RegisterWindow\registerwindow.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\RegisterWindow\registerwindow.cpp

CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\RegisterWindow\registerwindow.cpp > CMakeFiles\LMS.dir\window\RegisterWindow\registerwindow.cpp.i

CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\RegisterWindow\registerwindow.cpp -o CMakeFiles\LMS.dir\window\RegisterWindow\registerwindow.cpp.s

CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/mainWindow/mainwindow.cpp
CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj -MF CMakeFiles\LMS.dir\window\mainWindow\mainwindow.cpp.obj.d -o CMakeFiles\LMS.dir\window\mainWindow\mainwindow.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\mainWindow\mainwindow.cpp

CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\mainWindow\mainwindow.cpp > CMakeFiles\LMS.dir\window\mainWindow\mainwindow.cpp.i

CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\mainWindow\mainwindow.cpp -o CMakeFiles\LMS.dir\window\mainWindow\mainwindow.cpp.s

CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/adminWindow/adminwindow.cpp
CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj -MF CMakeFiles\LMS.dir\window\adminWindow\adminwindow.cpp.obj.d -o CMakeFiles\LMS.dir\window\adminWindow\adminwindow.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\adminWindow\adminwindow.cpp

CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\adminWindow\adminwindow.cpp > CMakeFiles\LMS.dir\window\adminWindow\adminwindow.cpp.i

CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\adminWindow\adminwindow.cpp -o CMakeFiles\LMS.dir\window\adminWindow\adminwindow.cpp.s

CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/borrowInfoDialog/borrowinfodialog.cpp
CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj -MF CMakeFiles\LMS.dir\window\borrowInfoDialog\borrowinfodialog.cpp.obj.d -o CMakeFiles\LMS.dir\window\borrowInfoDialog\borrowinfodialog.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\borrowInfoDialog\borrowinfodialog.cpp

CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\borrowInfoDialog\borrowinfodialog.cpp > CMakeFiles\LMS.dir\window\borrowInfoDialog\borrowinfodialog.cpp.i

CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\borrowInfoDialog\borrowinfodialog.cpp -o CMakeFiles\LMS.dir\window\borrowInfoDialog\borrowinfodialog.cpp.s

CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/window/bookEditDialog/bookeditdialog.cpp
CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj -MF CMakeFiles\LMS.dir\window\bookEditDialog\bookeditdialog.cpp.obj.d -o CMakeFiles\LMS.dir\window\bookEditDialog\bookeditdialog.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\window\bookEditDialog\bookeditdialog.cpp

CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\window\bookEditDialog\bookeditdialog.cpp > CMakeFiles\LMS.dir\window\bookEditDialog\bookeditdialog.cpp.i

CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\window\bookEditDialog\bookeditdialog.cpp -o CMakeFiles\LMS.dir\window\bookEditDialog\bookeditdialog.cpp.s

CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj: D:/course/DevOps/LMS-main20250704/LMS-main/util/CompressionUtil.cpp
CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj -MF CMakeFiles\LMS.dir\util\CompressionUtil.cpp.obj.d -o CMakeFiles\LMS.dir\util\CompressionUtil.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\util\CompressionUtil.cpp

CMakeFiles/LMS.dir/util/CompressionUtil.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/util/CompressionUtil.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\util\CompressionUtil.cpp > CMakeFiles\LMS.dir\util\CompressionUtil.cpp.i

CMakeFiles/LMS.dir/util/CompressionUtil.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/util/CompressionUtil.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\util\CompressionUtil.cpp -o CMakeFiles\LMS.dir\util\CompressionUtil.cpp.s

CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj: CMakeFiles/LMS.dir/flags.make
CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj: CMakeFiles/LMS.dir/includes_CXX.rsp
CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj: LMS_autogen/3YJK5W5UP7/qrc_icon.cpp
CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj: CMakeFiles/LMS.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj -MF CMakeFiles\LMS.dir\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp.obj.d -o CMakeFiles\LMS.dir\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp.obj -c D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp

CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp > CMakeFiles\LMS.dir\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp.i

CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s"
	D:\tdm-gcc\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\course\DevOps\LMS-main20250704\LMS-main\out\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp -o CMakeFiles\LMS.dir\LMS_autogen\3YJK5W5UP7\qrc_icon.cpp.s

# Object files for target LMS
LMS_OBJECTS = \
"CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/LMS.dir/src/main.cpp.obj" \
"CMakeFiles/LMS.dir/src/Book.cpp.obj" \
"CMakeFiles/LMS.dir/src/User.cpp.obj" \
"CMakeFiles/LMS.dir/src/Library.cpp.obj" \
"CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj" \
"CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj" \
"CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj" \
"CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj" \
"CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj" \
"CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj" \
"CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj" \
"CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj" \
"CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj"

# External object files for target LMS
LMS_EXTERNAL_OBJECTS =

LMS.exe: CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/src/main.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/src/Book.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/src/User.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/src/Library.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj
LMS.exe: CMakeFiles/LMS.dir/build.make
LMS.exe: D:/QT/6.9.1/msvc2022_64/lib/Qt6Widgets.lib
LMS.exe: D:/QT/6.9.1/msvc2022_64/lib/Qt6Network.lib
LMS.exe: D:/QT/6.9.1/msvc2022_64/lib/Qt6Gui.lib
LMS.exe: D:/QT/6.9.1/msvc2022_64/lib/Qt6Core.lib
LMS.exe: D:/QT/6.9.1/msvc2022_64/lib/Qt6EntryPoint.lib
LMS.exe: CMakeFiles/LMS.dir/linkLibs.rsp
LMS.exe: CMakeFiles/LMS.dir/objects1.rsp
LMS.exe: CMakeFiles/LMS.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Linking CXX executable LMS.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\LMS.dir\link.txt --verbose=$(VERBOSE)
	C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS.exe -installedDir D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/installed/x64-mingw-static/bin -OutVariable out

# Rule to build all files generated by this target.
CMakeFiles/LMS.dir/build: LMS.exe
.PHONY : CMakeFiles/LMS.dir/build

CMakeFiles/LMS.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\LMS.dir\cmake_clean.cmake
.PHONY : CMakeFiles/LMS.dir/clean

CMakeFiles/LMS.dir/depend: LMS_autogen/3YJK5W5UP7/qrc_icon.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\course\DevOps\LMS-main20250704\LMS-main D:\course\DevOps\LMS-main20250704\LMS-main D:\course\DevOps\LMS-main20250704\LMS-main\out D:\course\DevOps\LMS-main20250704\LMS-main\out D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles\LMS.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/LMS.dir/depend

