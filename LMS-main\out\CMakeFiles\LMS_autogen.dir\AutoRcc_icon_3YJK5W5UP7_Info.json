{"BUILD_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen", "CMAKE_BINARY_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main/out", "CMAKE_CURRENT_BINARY_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main/out", "CMAKE_CURRENT_SOURCE_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main", "CMAKE_SOURCE_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main", "CROSS_CONFIG": false, "GENERATOR": "MinGW Makefiles", "INCLUDE_DIR": "D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/include", "INPUTS": ["D:/course/DevOps/LMS-main20250704/LMS-main/resources/favicon.png"], "LOCK_FILE": "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/LMS_autogen.dir/AutoRcc_icon_3YJK5W5UP7_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["--no-zstd", "-name", "icon"], "OUTPUT_CHECKSUM": "3YJK5W5UP7", "OUTPUT_NAME": "qrc_icon.cpp", "RCC_EXECUTABLE": "D:/QT/6.9.1/msvc2022_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/LMS_autogen.dir/AutoRcc_icon_3YJK5W5UP7_Used.txt", "SOURCE": "D:/course/DevOps/LMS-main20250704/LMS-main/resources/icon.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}