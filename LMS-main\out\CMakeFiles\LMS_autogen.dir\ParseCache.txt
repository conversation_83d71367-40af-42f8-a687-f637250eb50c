# Generated by CMake. Changes will be overwritten.
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/adminwindow.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonValue
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/QStandardItemModel
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qstandarditemmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMainWindow
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmainwindow.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/adminwindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
D:/course/DevOps/LMS-main20250704/LMS-main/src/User.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/CompressionUtil.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/borrowinfodialog.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstractitemmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qitemselectionmodel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qvalidator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QTableWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractscrollarea.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractslider.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractspinbox.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qframe.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qrubberband.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qslider.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyle.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyleoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabbar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtableview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtablewidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/borrowinfodialog.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/bookeditdialog.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/bookeditdialog.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/forgotpasswordwindow.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonValue
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpen.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpicture.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextdocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtextoption.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLabel
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QLineEdit
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QPushButton
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QVBoxLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qboxlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qframe.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgridlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlabel.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlineedit.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qpushbutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/forgotpasswordwindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/loginwindow.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonValue
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QVBoxLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qboxlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgridlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/loginwindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/util/CompressionUtil.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/mainwindow.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonValue
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QMainWindow
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QRadioButton
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractbutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qmainwindow.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qradiobutton.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/mainwindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/include/library/registerwindow.h
 mmc:Q_OBJECT
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QFlags
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QIODevice
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonArray
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonDocument
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QJsonValue
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QList
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QMetaType
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QObject
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QSharedDataPointer
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QString
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QUrl
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/QVariant
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/q26numeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcalendar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborcommon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcborvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qcryptographichash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatetime.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonarray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsondocument.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonparseerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qjsonvalue.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qurl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/quuid.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkAccessManager
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkReply
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QNetworkRequest
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslConfiguration
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qabstractsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhostaddress.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qhttpheaders.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkaccessmanager.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkreply.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qnetworkrequest.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qssl.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslcertificate.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslconfiguration.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslerror.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qsslsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtcpsocket.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetwork-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtNetwork/qtnetworkglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QVBoxLayout
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QWidget
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qboxlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qgridlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayout.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qlayoutitem.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Book.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/Library.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/User.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/loginWindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/include/library/registerwindow.h
 mdp:D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h
 mdp:D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/process.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/string.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/time.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h
 mdp:D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
D:/course/DevOps/LMS-main20250704/LMS-main/window/forgotpasswordwindow/forgotpasswordwindow.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/src/Book.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/src/Library.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/src/main.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/window/mainWindow/mainwindow.cpp
 uic:ui_MainWindow.h
D:/course/DevOps/LMS-main20250704/LMS-main/window/RegisterWindow/registerwindow.cpp
D:/course/DevOps/LMS-main20250704/LMS-main/window/adminWindow/adminwindow.cpp
 uic:ui_AdminWindow.h
D:/course/DevOps/LMS-main20250704/LMS-main/window/bookEditDialog/bookeditdialog.cpp
 uic:ui_bookeditdialog.h
D:/course/DevOps/LMS-main20250704/LMS-main/window/borrowInfoDialog/borrowinfodialog.cpp
 uic:ui_BorrowInfoDialog.h
D:/course/DevOps/LMS-main20250704/LMS-main/window/loginWindow/loginwindow.cpp
