# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\course\DevOps\LMS-main20250704\LMS-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\course\DevOps\LMS-main20250704\LMS-main\out

# Utility rule file for LMS_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/LMS_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/LMS_autogen.dir/progress.make

CMakeFiles/LMS_autogen: LMS_autogen/timestamp

LMS_autogen/timestamp: D:/QT/6.9.1/msvc2022_64/bin/moc.exe
LMS_autogen/timestamp: D:/QT/6.9.1/msvc2022_64/bin/uic.exe
LMS_autogen/timestamp: CMakeFiles/LMS_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target LMS"
	"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen D:/course/DevOps/LMS-main20250704/LMS-main/out/CMakeFiles/LMS_autogen.dir/AutogenInfo.json ""
	"C:\Program Files\CMake\bin\cmake.exe" -E touch D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/timestamp

CMakeFiles/LMS_autogen.dir/codegen:
.PHONY : CMakeFiles/LMS_autogen.dir/codegen

LMS_autogen: CMakeFiles/LMS_autogen
LMS_autogen: LMS_autogen/timestamp
LMS_autogen: CMakeFiles/LMS_autogen.dir/build.make
.PHONY : LMS_autogen

# Rule to build all files generated by this target.
CMakeFiles/LMS_autogen.dir/build: LMS_autogen
.PHONY : CMakeFiles/LMS_autogen.dir/build

CMakeFiles/LMS_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\LMS_autogen.dir\cmake_clean.cmake
.PHONY : CMakeFiles/LMS_autogen.dir/clean

CMakeFiles/LMS_autogen.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\course\DevOps\LMS-main20250704\LMS-main D:\course\DevOps\LMS-main20250704\LMS-main D:\course\DevOps\LMS-main20250704\LMS-main\out D:\course\DevOps\LMS-main20250704\LMS-main\out D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles\LMS_autogen.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/LMS_autogen.dir/depend

