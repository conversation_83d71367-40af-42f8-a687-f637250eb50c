# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\course\DevOps\LMS-main20250704\LMS-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\course\DevOps\LMS-main20250704\LMS-main\out

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/LMS.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/LMS.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/LMS.dir/clean
clean: CMakeFiles/LMS_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/LMS_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/LMS.dir

# All Build rule for target.
CMakeFiles/LMS.dir/all: CMakeFiles/LMS_autogen_timestamp_deps.dir/all
CMakeFiles/LMS.dir/all: CMakeFiles/LMS_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16 "Built target LMS"
.PHONY : CMakeFiles/LMS.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/LMS.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/LMS.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 0
.PHONY : CMakeFiles/LMS.dir/rule

# Convenience name for target.
LMS: CMakeFiles/LMS.dir/rule
.PHONY : LMS

# codegen rule for target.
CMakeFiles/LMS.dir/codegen: CMakeFiles/LMS_autogen_timestamp_deps.dir/all
CMakeFiles/LMS.dir/codegen: CMakeFiles/LMS_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16 "Finished codegen for target LMS"
.PHONY : CMakeFiles/LMS.dir/codegen

# clean rule for target.
CMakeFiles/LMS.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/clean
.PHONY : CMakeFiles/LMS.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/LMS_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/LMS_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen_timestamp_deps.dir\build.make CMakeFiles/LMS_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen_timestamp_deps.dir\build.make CMakeFiles/LMS_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num= "Built target LMS_autogen_timestamp_deps"
.PHONY : CMakeFiles/LMS_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/LMS_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/LMS_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 0
.PHONY : CMakeFiles/LMS_autogen_timestamp_deps.dir/rule

# Convenience name for target.
LMS_autogen_timestamp_deps: CMakeFiles/LMS_autogen_timestamp_deps.dir/rule
.PHONY : LMS_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/LMS_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen_timestamp_deps.dir\build.make CMakeFiles/LMS_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num= "Finished codegen for target LMS_autogen_timestamp_deps"
.PHONY : CMakeFiles/LMS_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/LMS_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen_timestamp_deps.dir\build.make CMakeFiles/LMS_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/LMS_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/LMS_autogen.dir

# All Build rule for target.
CMakeFiles/LMS_autogen.dir/all: CMakeFiles/LMS_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen.dir\build.make CMakeFiles/LMS_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen.dir\build.make CMakeFiles/LMS_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=17 "Built target LMS_autogen"
.PHONY : CMakeFiles/LMS_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/LMS_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/LMS_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 0
.PHONY : CMakeFiles/LMS_autogen.dir/rule

# Convenience name for target.
LMS_autogen: CMakeFiles/LMS_autogen.dir/rule
.PHONY : LMS_autogen

# codegen rule for target.
CMakeFiles/LMS_autogen.dir/codegen: CMakeFiles/LMS_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen.dir\build.make CMakeFiles/LMS_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles --progress-num=17 "Finished codegen for target LMS_autogen"
.PHONY : CMakeFiles/LMS_autogen.dir/codegen

# clean rule for target.
CMakeFiles/LMS_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen.dir\build.make CMakeFiles/LMS_autogen.dir/clean
.PHONY : CMakeFiles/LMS_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

