/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // favicon.png
  0x0,0x0,0xc,0x57,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0xc8,0x0,0x0,0x0,0xc8,0x8,0x6,0x0,0x0,0x0,0xad,0x58,0xae,0x9e,
  0x0,0x0,0x0,0x1,0x73,0x52,0x47,0x42,0x0,0xae,0xce,0x1c,0xe9,0x0,0x0,0xc,
  0x11,0x49,0x44,0x41,0x54,0x78,0x5e,0xed,0x9d,0x4d,0x76,0xdb,0x36,0x17,0x40,0x1f,
  0x68,0x2f,0xa0,0x75,0xbf,0xb9,0xd5,0x81,0x73,0x4e,0x57,0x11,0x7b,0x25,0xb1,0x87,
  0x8e,0x17,0x91,0x78,0x11,0x49,0x86,0x76,0x56,0x12,0x75,0x15,0x3d,0x27,0x1e,0x94,
  0x99,0xb7,0x6e,0x17,0x60,0x13,0xdf,0x1,0x25,0xc5,0x8a,0x2a,0x91,0x4,0x48,0x3e,
  0xf2,0x91,0xd7,0x93,0xfc,0x18,0x20,0x80,0xfb,0x78,0xf5,0x0,0xf0,0x47,0x4e,0xf8,
  0x81,0x0,0x4,0xe,0x12,0x70,0xb0,0x81,0x0,0x4,0xe,0x13,0x40,0x10,0xce,0xe,
  0x8,0x54,0x10,0x40,0x10,0x4e,0xf,0x8,0x20,0x8,0xe7,0x0,0x4,0xd2,0x8,0x90,
  0x41,0xd2,0xb8,0xb5,0xae,0xf5,0xbf,0x4f,0xf,0xe7,0xbe,0xf0,0xb,0x2f,0x6e,0x51,
  0x1e,0x2c,0xf3,0xa7,0xde,0xaf,0xfe,0xee,0x9c,0xcf,0xcb,0x3f,0xb,0xf9,0xbd,0xfc,
  0x33,0x73,0xf9,0x5f,0xd7,0x67,0xcb,0xd6,0x8d,0x72,0x80,0x68,0x2,0x8,0x12,0x8d,
  0x2c,0xad,0xc2,0x4f,0x9f,0xfe,0x58,0x64,0xc5,0xf1,0x65,0x10,0x41,0xbc,0xbb,0x8c,
  0x3f,0x8a,0xcf,0xc5,0xc9,0x32,0x48,0xf3,0xf7,0xcd,0xab,0xfb,0xf8,0xfa,0xd4,0x48,
  0x21,0x80,0x20,0x29,0xd4,0x22,0xea,0x9c,0x7c,0x78,0x78,0xef,0x9d,0xbc,0x76,0x22,
  0xe7,0x11,0xd5,0x1a,0x14,0xf5,0xb9,0x78,0xf7,0xf9,0xf1,0xe6,0xec,0x7d,0x83,0xc2,
  0x14,0x49,0x24,0x80,0x20,0x89,0xe0,0xea,0xaa,0x85,0x29,0x54,0xe1,0xfd,0x9d,0x6c,
  0xa6,0x50,0x75,0x15,0x92,0x7f,0xef,0xf3,0xcc,0xb9,0x2b,0xa6,0x60,0xc9,0x0,0x2b,
  0x2b,0x22,0x48,0xc7,0x5c,0xc3,0x54,0xca,0xf9,0xe3,0xbb,0xee,0x33,0x46,0x5d,0x47,
  0x11,0xa5,0x8e,0x50,0xca,0xef,0x11,0x24,0x85,0xda,0x81,0x3a,0x61,0x3a,0x25,0x4e,
  0xde,0x75,0x78,0xc8,0xf8,0x43,0x79,0xb9,0x65,0xda,0x15,0x8f,0xed,0x50,0xd,0x4,
  0xe9,0x88,0xe5,0xcf,0x1f,0x1f,0xbe,0xe8,0x67,0x8d,0x3,0x9d,0x77,0xfe,0xfe,0xf1,
  0xfa,0xd5,0x55,0x47,0x43,0x9b,0xf5,0x61,0x10,0xa4,0x65,0xf8,0x87,0x9b,0x52,0xd5,
  0x75,0xdc,0xe7,0x85,0x7b,0xbe,0xf8,0xf7,0xfa,0xb7,0x72,0xcb,0x98,0x9f,0x34,0x2,
  0x8,0x92,0xc6,0xad,0xac,0x55,0x6e,0xdd,0xfa,0xe3,0x3f,0x5b,0x1c,0xa2,0xe7,0xaa,
  0x48,0xd2,0x16,0x30,0x82,0xb4,0x20,0x78,0xf2,0xf1,0xeb,0x9f,0xfd,0xef,0x52,0xb5,
  0xe8,0x60,0x59,0x15,0x49,0xda,0x10,0x44,0x90,0x44,0x7a,0xa3,0x5a,0x73,0xd4,0x8e,
  0xc1,0xe7,0x8f,0x6f,0x5f,0xfd,0x5a,0x5b,0x8c,0x2,0xff,0x21,0x80,0x20,0x9,0x27,
  0x85,0x2d,0x39,0xd6,0x3,0x64,0xe1,0x9e,0x10,0x69,0x11,0x4,0x89,0xc4,0xf6,0xcb,
  0x87,0xaf,0x97,0xde,0xb9,0xbb,0xc8,0x6a,0xe3,0x28,0xce,0x16,0x70,0x74,0x1c,0x10,
  0x24,0x12,0xd9,0xc9,0xc7,0x7,0x1f,0x59,0x65,0x44,0xc5,0x59,0x8f,0xc4,0x6,0x3,
  0x41,0x22,0x88,0x9d,0x7c,0xfa,0x7a,0x97,0x76,0xa3,0x61,0x44,0x23,0x7d,0x17,0x65,
  0xaa,0x15,0x45,0x18,0x41,0x1a,0xe2,0x1a,0xff,0x96,0x6e,0xc3,0x81,0x8,0xb7,0xa4,
  0x34,0x25,0x15,0xca,0x21,0x48,0x43,0x5a,0x26,0x17,0xe6,0x87,0xc6,0x46,0x16,0x69,
  0x18,0x75,0x4,0x69,0x4,0x6a,0x75,0x67,0xae,0x7c,0x69,0x54,0xd8,0x48,0xa1,0xcc,
  0xc9,0x5,0x77,0x0,0xd7,0x7,0x8b,0xc,0x52,0xcf,0x48,0x26,0xb1,0xf6,0xd8,0x1d,
  0x27,0x3b,0x5a,0xd,0x22,0x4f,0x6,0x69,0x4,0xc9,0xc6,0x15,0xf3,0x46,0x43,0xd9,
  0x2a,0xc4,0xc5,0xc3,0x26,0xc4,0xc8,0x20,0xd,0x28,0xd9,0xde,0xda,0x3d,0x3c,0x40,
  0xa6,0x59,0xf5,0xc1,0x47,0x90,0x1a,0x46,0xa6,0x2f,0xc,0xd6,0xc5,0x9f,0x69,0x56,
  0x1d,0x21,0x76,0xb1,0xea,0x8,0x4d,0x6a,0xf7,0x6a,0x67,0xb0,0x5e,0x64,0xf9,0xcf,
  0xdb,0xb3,0x8b,0x3a,0x6,0x73,0xfe,0x3d,0x19,0xa4,0x26,0xfa,0xd3,0x5c,0x7f,0x6c,
  0x6,0xcd,0x3a,0xa4,0x4e,0x7e,0x4,0xa9,0x15,0xc4,0xf2,0xad,0x25,0x75,0xe1,0x17,
  0x79,0x7c,0x7b,0xc6,0x39,0x50,0x81,0x9,0x38,0x15,0x70,0xa6,0x73,0xf5,0xfc,0xf0,
  0x20,0xb,0xf7,0xf4,0x2b,0x4f,0x1d,0x1e,0xe6,0x83,0x20,0x15,0x82,0x4c,0xf1,0x2,
  0xe1,0xee,0x70,0xd9,0xc9,0xaa,0xce,0xb2,0x8,0x52,0xc1,0x67,0xd2,0x3b,0x58,0xeb,
  0x71,0x23,0x8,0x82,0xd4,0x4f,0xc4,0xf,0x94,0x98,0x83,0x20,0xc2,0x56,0x6f,0xe5,
  0xf9,0x41,0x6,0x99,0x79,0x6,0x71,0xde,0x5f,0xf1,0xae,0x5f,0xd6,0x20,0x49,0x59,
  0x84,0x35,0x48,0x12,0xb6,0x49,0x55,0x22,0x83,0xb0,0x8b,0xc5,0x2e,0x16,0xdb,0xbc,
  0x69,0x1f,0x6a,0x73,0xd8,0xe6,0xe5,0x3a,0x8,0x8b,0xf4,0x34,0x3b,0xd6,0xb5,0xa6,
  0x7a,0xa3,0xe2,0x6,0xa,0x82,0x20,0x48,0x2b,0x41,0xb8,0x17,0xab,0x15,0x3e,0xf3,
  0x95,0x59,0x83,0xd4,0xdd,0x6a,0x32,0x86,0x37,0xb6,0xf7,0x75,0x9a,0xf1,0xe8,0x6d,
  0x2d,0x59,0x4,0xa9,0x41,0x34,0xe5,0x9d,0x2c,0x6e,0x33,0xa9,0xf5,0x83,0xdb,0xdd,
  0xeb,0x11,0x89,0x4c,0x75,0x9a,0xc5,0xfa,0xa3,0x3e,0xfa,0x64,0x90,0x7a,0x46,0x32,
  0x8a,0x2f,0xc6,0x69,0xd0,0xcf,0xa8,0x22,0x4c,0xaf,0x1a,0xe1,0x42,0x90,0x6,0x98,
  0xa6,0x38,0xcd,0xe2,0x1e,0xac,0x6,0x81,0xe7,0xbd,0x58,0xcd,0x20,0x85,0x52,0x53,
  0x9a,0x66,0xf1,0x24,0x61,0xf3,0xb8,0x93,0x41,0x1a,0xb2,0x9a,0x52,0x16,0x21,0x7b,
  0x34,0xc,0x3a,0x19,0xa4,0x39,0xa8,0xa9,0x64,0x11,0xb2,0x47,0x5c,0xcc,0xc9,0x20,
  0x11,0xbc,0xa6,0x70,0xeb,0x9,0xd9,0x23,0x22,0xe0,0x64,0x90,0x38,0x58,0xa1,0xb4,
  0xe9,0x1d,0x2d,0x76,0xae,0xa2,0x3,0x4e,0x6,0x89,0x44,0x36,0xde,0x6f,0xb5,0xad,
  0x1e,0x8,0x53,0xab,0xc8,0x40,0xaf,0x8b,0x23,0x48,0x2,0xb7,0xd5,0x54,0xeb,0xe8,
  0xcb,0xf8,0xbf,0xc0,0x73,0x33,0x38,0x5e,0xef,0x93,0x10,0xe6,0xb2,0xa,0x82,0x24,
  0x92,0xb3,0xb4,0x1e,0x61,0xdd,0x91,0x18,0x64,0x4,0x49,0x7,0x17,0x6a,0x5a,0x78,
  0x66,0x1d,0x39,0xda,0xc5,0x98,0xc,0xd2,0x8e,0x9f,0x8c,0xf9,0xfa,0x8,0x72,0xb4,
  0xc,0x2e,0x19,0xa4,0x3d,0xc0,0x70,0x84,0xf1,0xad,0x49,0xf8,0x9a,0xb5,0x6e,0x22,
  0xcb,0x1a,0xa4,0x2b,0x8e,0x2b,0x49,0xe4,0xe8,0xdd,0xd0,0x5f,0xf2,0xc9,0x6e,0x55,
  0x67,0x21,0x65,0x91,0xde,0x2d,0xca,0x75,0x26,0x29,0x8e,0x2f,0xc5,0xc9,0xbb,0xae,
  0x8f,0x5d,0x7f,0x3c,0x9f,0x8b,0x77,0x9f,0x1f,0x6f,0xce,0xde,0xd7,0x97,0xa5,0x44,
  0x53,0x2,0xac,0x41,0x9a,0x92,0x8a,0x28,0x57,0x66,0x13,0x4d,0x51,0x78,0xf9,0x5b,
  0x44,0x74,0xe2,0x8a,0x22,0x48,0x1c,0xaf,0xa8,0xd2,0xbd,0x8b,0x82,0x18,0x51,0xf1,
  0x48,0x29,0x8c,0x20,0x29,0xd4,0x22,0xeb,0x4,0x51,0x8e,0x8a,0xa3,0x73,0x9f,0xc9,
  0xeb,0x76,0x6b,0x94,0xd5,0x34,0x2a,0x34,0xcf,0x54,0x2a,0x32,0x8,0x89,0xc5,0x11,
  0x24,0x11,0x5c,0x6a,0xb5,0x20,0xcb,0xb1,0x1c,0x2f,0x7c,0xe1,0x17,0x41,0x18,0xef,
  0xdd,0x22,0x1c,0xcb,0x89,0x9c,0xbf,0x1c,0xd3,0xe7,0xe1,0xef,0x5e,0x5c,0xee,0x9c,
  0xcf,0xa5,0x70,0xdf,0xb2,0x4c,0x96,0x7c,0x6d,0x73,0x2a,0xf5,0xf4,0x7a,0xc9,0x82,
  0xac,0xf6,0xff,0xfd,0xdd,0x26,0x90,0x9b,0x2e,0x94,0x1,0xdd,0xfc,0x14,0xee,0x9b,
  0x93,0x97,0x7f,0xbb,0xcc,0xe5,0x4f,0xf2,0x94,0xf3,0x7d,0x14,0xfb,0x3,0x16,0xe4,
  0x81,0xcd,0xe1,0x93,0x79,0xfb,0xc3,0x65,0x7d,0xde,0x95,0x1f,0x2e,0xe5,0x4f,0xe6,
  0x4f,0xcb,0xff,0x5b,0x7f,0xe0,0xac,0x3e,0x74,0xfc,0xea,0xc3,0xc7,0xcb,0x6d,0xea,
  0xfb,0x87,0x93,0x5,0x69,0x77,0x15,0x79,0x2d,0x8d,0x93,0x65,0x39,0x80,0x42,0x7e,
  0xf,0xf2,0xf0,0x9,0x99,0xfe,0x49,0x37,0x95,0x9a,0x41,0x82,0x30,0x96,0x72,0x4a,
  0x2a,0x6e,0x11,0x4e,0xfc,0x70,0xd2,0xff,0x98,0x61,0xe3,0x46,0xdb,0xe6,0x5,0xdd,
  0x3,0x9,0x52,0x35,0xc0,0x17,0x79,0x10,0x27,0xee,0x44,0xb0,0x56,0xfa,0xfb,0x26,
  0x46,0xf8,0xe4,0x77,0xf2,0xba,0x8d,0x4,0x55,0x63,0x9f,0x98,0x20,0xfb,0x86,0xea,
  0x73,0x71,0xb2,0x44,0x18,0x6b,0xa,0xfc,0xd8,0xdf,0x8d,0x10,0x7d,0xca,0xb0,0x8f,
  0xd0,0xc,0x4,0xd9,0x1d,0xf6,0x4a,0x18,0x16,0xaf,0xe3,0x16,0x66,0x28,0x21,0x76,
  0xa9,0xcc,0x50,0x90,0xfd,0xc2,0x84,0xc,0x93,0xba,0x18,0x1b,0xf7,0xa9,0x66,0xa7,
  0x77,0xe5,0x13,0x97,0x61,0xc1,0xec,0xdd,0xe5,0x58,0x7a,0x8d,0x20,0x3f,0x44,0xe2,
  0x65,0x3a,0x86,0x2c,0x3a,0xa7,0x68,0xb9,0xa3,0x59,0xc8,0xf9,0x30,0xb7,0xd8,0xd4,
  0x8f,0x11,0x41,0xe,0x32,0x5a,0xc9,0x92,0x89,0xfb,0xcc,0xe,0x59,0xfd,0x89,0x14,
  0x53,0xa2,0xf7,0xbb,0x4,0x62,0x3a,0x53,0x53,0x16,0x41,0x1a,0xc1,0x44,0x96,0x46,
  0x98,0x2a,0xa,0x59,0x92,0x62,0x7b,0x18,0x8,0x12,0x1d,0x79,0xee,0x7c,0x8d,0x41,
  0xb6,0x7a,0x93,0x8b,0x7f,0x63,0xe7,0x19,0xfc,0x1f,0x47,0x87,0x20,0x31,0xd1,0xde,
  0x5d,0xaf,0x70,0x8b,0xf8,0x5e,0x7a,0x56,0xb3,0x5,0xdb,0xbc,0xc9,0x32,0x54,0x55,
  0x5c,0x65,0x94,0x22,0x7b,0xba,0x9f,0xfb,0xad,0x1e,0x53,0x12,0x63,0x13,0x71,0x32,
  0x48,0x67,0xd2,0xcc,0x77,0x9d,0x32,0x45,0x31,0x10,0xa4,0x33,0x31,0xf6,0x1c,0xc8,
  0xf9,0xfb,0x39,0xec,0x7e,0x8d,0xe5,0x51,0xe1,0x7e,0x43,0xe9,0xaf,0x52,0xb7,0xfc,
  0x47,0x78,0x2f,0x56,0x9f,0xa8,0xe2,0x8f,0x1d,0x9e,0xf1,0xf6,0xee,0xe9,0x6a,0x6a,
  0x53,0x2f,0xab,0x6f,0x88,0x8c,0x8f,0x60,0xb8,0x9b,0x17,0x41,0x52,0xb8,0x45,0xd4,
  0x99,0xce,0xae,0xd7,0x94,0xa7,0x52,0x87,0x2,0x8a,0x20,0x11,0xa7,0x7a,0xbb,0xa2,
  0xb6,0x45,0x31,0xfd,0xe2,0xed,0x16,0x81,0x43,0x90,0x16,0xf0,0xd2,0xaa,0xda,0x7a,
  0xef,0xd4,0xcb,0xc3,0x6d,0xab,0xa7,0x17,0xe7,0xf6,0x83,0x20,0x43,0x45,0xdc,0xf9,
  0xfb,0x42,0x9e,0x6f,0xc7,0xba,0x3e,0x99,0xd3,0x3a,0xa3,0xea,0x14,0x40,0x90,0xa1,
  0x4,0x29,0xdb,0x1d,0xe7,0xb4,0x6b,0xae,0xd3,0x29,0x2e,0x14,0xe,0x2a,0x43,0xf5,
  0xc5,0xc6,0xc2,0x3d,0x5f,0xc,0x9d,0x4d,0xc6,0xf7,0x1a,0xd4,0xe1,0x3,0x46,0x6,
  0x19,0x3e,0x6,0xeb,0x1e,0xc,0x9b,0x4d,0xc8,0x1a,0xfb,0x4f,0x4,0x4,0x19,0x8d,
  0x20,0x9b,0x8e,0xf8,0x5c,0x33,0x9b,0xb0,0xd6,0xa8,0x3e,0x1,0x10,0x64,0x74,0x82,
  0xe8,0xad,0x4d,0xc6,0xfc,0xf5,0xb,0x63,0x9,0xb,0x82,0x8c,0x25,0x12,0xfb,0xfa,
  0xd1,0xd3,0xeb,0x41,0xc9,0x1a,0xcd,0x83,0x8e,0x20,0xcd,0x59,0xd,0x54,0xb2,0xdb,
  0xeb,0x26,0x64,0x8d,0xb8,0x30,0x22,0x48,0x1c,0xaf,0x81,0x4a,0x77,0xb3,0x80,0x67,
  0x21,0x1e,0x1f,0x3e,0x4,0x89,0x67,0x36,0x5c,0x8d,0x16,0x53,0xae,0x9f,0x3f,0x3e,
  0x7c,0xe9,0xeb,0xe5,0x6a,0xc3,0x1,0xe9,0xbf,0x65,0x4,0xe9,0x9f,0x71,0xc7,0x2d,
  0xc4,0xed,0x72,0xb1,0xde,0x68,0x87,0x1f,0x41,0xda,0xf1,0x1b,0xa8,0x76,0x33,0x49,
  0xb8,0xf0,0xd7,0x3e,0x3c,0x8,0xd2,0x9e,0xe1,0x40,0x47,0xf0,0x79,0xd5,0x9b,0xc7,
  0x59,0x8c,0x77,0x13,0x16,0x4,0xe9,0x86,0xe3,0x40,0x47,0xd9,0xbf,0x78,0x6f,0xf7,
  0xf6,0xfc,0x81,0x86,0x32,0xd2,0x66,0x11,0x64,0xa4,0x81,0x89,0xea,0xd6,0xd6,0xe2,
  0x9d,0x9d,0xaa,0x28,0x72,0xb5,0x85,0x11,0xa4,0x16,0x91,0x91,0x2,0xce,0xdf,0xb7,
  0xfd,0x2e,0xc,0x23,0x23,0x55,0xed,0x26,0x82,0xa8,0xe2,0xa6,0x31,0x6b,0x4,0x10,
  0xc4,0x5a,0xc4,0xe8,0xaf,0x2a,0x1,0x4,0x51,0xc5,0x4d,0x63,0xd6,0x8,0x20,0x88,
  0xb5,0x88,0xd1,0x5f,0x55,0x2,0x8,0xa2,0x8a,0x9b,0xc6,0xac,0x11,0x40,0x10,0x6b,
  0x11,0xa3,0xbf,0xaa,0x4,0x10,0x44,0x15,0x37,0x8d,0x59,0x23,0x80,0x20,0xd6,0x22,
  0x46,0x7f,0x55,0x9,0x20,0x88,0x2a,0x6e,0x1a,0xb3,0x46,0x0,0x41,0xac,0x45,0x8c,
  0xfe,0xaa,0x12,0x40,0x10,0x55,0xdc,0x34,0x66,0x8d,0x0,0x82,0x58,0x8b,0x18,0xfd,
  0x55,0x25,0x80,0x20,0xaa,0xb8,0x69,0xcc,0x1a,0x1,0x4,0xb1,0x16,0x31,0xfa,0xab,
  0x4a,0x0,0x41,0x54,0x71,0xd3,0x98,0x35,0x2,0x8,0x62,0x2d,0x62,0xf4,0x57,0x95,
  0x0,0x82,0xa8,0xe2,0xa6,0x31,0x6b,0x4,0x10,0xc4,0x5a,0xc4,0xe8,0xaf,0x2a,0x1,
  0x4,0x51,0xc5,0x4d,0x63,0xd6,0x8,0x20,0x88,0xb5,0x88,0xd1,0x5f,0x55,0x2,0x8,
  0xa2,0x8a,0x9b,0xc6,0xac,0x11,0x40,0x10,0x6b,0x11,0xa3,0xbf,0xaa,0x4,0x10,0x44,
  0x15,0x37,0x8d,0x59,0x23,0x80,0x20,0xd6,0x22,0x46,0x7f,0x55,0x9,0x20,0x88,0x2a,
  0x6e,0x1a,0xb3,0x46,0x0,0x41,0xac,0x45,0x8c,0xfe,0xaa,0x12,0x40,0x10,0x55,0xdc,
  0x34,0x66,0x8d,0x0,0x82,0x58,0x8b,0x18,0xfd,0x55,0x25,0x80,0x20,0xaa,0xb8,0x69,
  0xcc,0x1a,0x1,0x4,0xb1,0x16,0x31,0xfa,0xab,0x4a,0x0,0x41,0x54,0x71,0xd3,0x98,
  0x35,0x2,0x8,0x62,0x2d,0x62,0xf4,0x57,0x95,0x0,0x82,0xa8,0xe2,0xa6,0x31,0x6b,
  0x4,0x10,0xc4,0x5a,0xc4,0xe8,0xaf,0x2a,0x1,0x4,0x51,0xc5,0x4d,0x63,0xd6,0x8,
  0x20,0x88,0xb5,0x88,0xd1,0x5f,0x55,0x2,0x8,0xa2,0x8a,0x9b,0xc6,0xac,0x11,0x40,
  0x10,0x6b,0x11,0xa3,0xbf,0xaa,0x4,0x10,0x44,0x15,0x37,0x8d,0x59,0x23,0x80,0x20,
  0xd6,0x22,0x46,0x7f,0x55,0x9,0x20,0x88,0x2a,0x6e,0x1a,0xb3,0x46,0x0,0x41,0xac,
  0x45,0x8c,0xfe,0xaa,0x12,0x40,0x10,0x55,0xdc,0x34,0x66,0x8d,0x0,0x82,0x58,0x8b,
  0x18,0xfd,0x55,0x25,0x80,0x20,0xaa,0xb8,0x69,0xcc,0x1a,0x1,0x4,0xb1,0x16,0x31,
  0xfa,0xab,0x4a,0x0,0x41,0x54,0x71,0xd3,0x98,0x35,0x2,0x8,0x62,0x2d,0x62,0xf4,
  0x57,0x95,0x0,0x82,0xa8,0xe2,0xa6,0x31,0x6b,0x4,0x10,0xc4,0x5a,0xc4,0xe8,0xaf,
  0x2a,0x1,0x4,0x51,0xc5,0x4d,0x63,0xd6,0x8,0x20,0x88,0xb5,0x88,0xd1,0x5f,0x55,
  0x2,0x8,0xa2,0x8a,0x9b,0xc6,0xac,0x11,0x40,0x10,0x6b,0x11,0xa3,0xbf,0xaa,0x4,
  0x10,0x44,0x15,0x37,0x8d,0x59,0x23,0x80,0x20,0xd6,0x22,0x46,0x7f,0x55,0x9,0x20,
  0x88,0x2a,0x6e,0x1a,0xb3,0x46,0x0,0x41,0xac,0x45,0x8c,0xfe,0xaa,0x12,0x40,0x10,
  0x55,0xdc,0x34,0x66,0x8d,0x0,0x82,0x58,0x8b,0x18,0xfd,0x55,0x25,0x80,0x20,0xaa,
  0xb8,0x69,0xcc,0x1a,0x1,0x4,0xb1,0x16,0x31,0xfa,0xab,0x4a,0x0,0x41,0x54,0x71,
  0xd3,0x98,0x35,0x2,0x8,0x62,0x2d,0x62,0xf4,0x57,0x95,0x0,0x82,0xa8,0xe2,0xa6,
  0x31,0x6b,0x4,0x10,0xc4,0x5a,0xc4,0xe8,0xaf,0x2a,0x1,0x4,0x51,0xc5,0x4d,0x63,
  0xd6,0x8,0x20,0x88,0xb5,0x88,0xd1,0x5f,0x55,0x2,0x8,0xa2,0x8a,0x9b,0xc6,0xac,
  0x11,0x40,0x10,0x6b,0x11,0xa3,0xbf,0xaa,0x4,0x6,0x11,0xe4,0xa7,0x4f,0x7f,0x2c,
  0x8e,0x8a,0xa3,0x73,0x2f,0x6e,0x51,0x8e,0x36,0xf3,0xa7,0x9b,0x51,0x7b,0xbf,0xfe,
  0x3f,0x11,0x71,0xe2,0x57,0xbf,0xdf,0x94,0x53,0x45,0x43,0x63,0xd3,0x24,0xe0,0xf3,
  0xed,0x71,0x79,0x71,0xe5,0xbf,0x9d,0x5b,0xff,0x7f,0xe1,0xbe,0x6d,0x7e,0xef,0xc4,
  0xe7,0xcf,0xd9,0xf3,0xf2,0xdf,0xeb,0xdf,0x7e,0xa8,0xd3,0x94,0x8b,0x6b,0x5a,0xb0,
  0x8b,0x72,0x41,0xaa,0x70,0x9c,0x20,0x56,0xf8,0xd3,0x67,0xf2,0x3a,0xc8,0xe4,0x44,
  0xca,0x7f,0xf3,0x3,0x81,0x17,0x2,0xeb,0x93,0xdd,0xc9,0x52,0xa,0xf7,0x2d,0x9c,
  0xe8,0x2e,0x73,0xf9,0x5f,0xd7,0x67,0x4b,0x4d,0x4a,0xaa,0x82,0x54,0xd,0x2c,0xc8,
  0x73,0x2c,0xc7,0xb,0x5f,0xf8,0x45,0x99,0x95,0x32,0x7f,0x8a,0x3c,0x9a,0xa7,0xc2,
  0x50,0x6d,0xf9,0x5c,0xd6,0x12,0x94,0x13,0x91,0x4c,0x96,0xda,0x12,0x54,0x8d,0x7c,
  0x34,0x82,0x1c,0xea,0xe4,0xf7,0xa9,0x1c,0xd9,0x66,0xa8,0x33,0xb8,0xe3,0x76,0x7d,
  0x2e,0xde,0x7d,0xe,0x7,0x7d,0xbc,0x39,0x7b,0xdf,0xf1,0xc1,0x3b,0x3f,0xdc,0xe8,
  0x5,0xd9,0x1d,0x31,0xc2,0x74,0x7e,0xe,0xf4,0x7c,0x40,0x5b,0x42,0xec,0xc2,0x30,
  0x27,0x48,0x95,0x30,0xe2,0xdd,0x65,0xcf,0xd1,0xe6,0xf0,0xb5,0x4,0x6c,0xb,0x31,
  0x39,0x41,0xb6,0x7,0xb4,0x9d,0x5d,0x90,0xa5,0xf6,0x4c,0xee,0xb4,0x80,0x17,0x59,
  0x66,0xde,0x7f,0xfe,0xfb,0xe6,0xd5,0x7d,0xa7,0x7,0x1e,0xf8,0x60,0xe6,0x33,0x48,
  0x93,0xb5,0xb,0xb2,0xf4,0x75,0x96,0xad,0xb2,0x85,0x85,0xb5,0x44,0x2a,0x81,0xc9,
  0xa,0xb2,0x2f,0xb3,0x14,0xce,0xbd,0x61,0x4b,0x39,0xf5,0x54,0xd9,0xd4,0x9b,0xbe,
  0x14,0xdb,0x84,0x66,0x21,0xc8,0xae,0x2c,0x59,0x71,0x7c,0x29,0xce,0xbf,0xe1,0xe2,
  0x65,0x53,0x59,0x56,0x52,0x14,0xd9,0xd3,0x7d,0xea,0x5,0xb7,0xa6,0x2d,0x8d,0xad,
  0xdc,0xec,0x4,0xd9,0x4,0x60,0xb3,0x5e,0x21,0xab,0x54,0x9d,0x92,0xf3,0xca,0x16,
  0xfb,0x48,0xcc,0x56,0x90,0x6d,0x18,0xbf,0x7c,0xf8,0x7a,0x89,0x28,0xdb,0x44,0x7c,
  0xee,0xbc,0xdc,0x4e,0x6d,0xc1,0x9d,0x92,0x9d,0x10,0x64,0x8b,0x5a,0xc8,0x2a,0x99,
  0x1c,0xbd,0x9b,0xef,0xa2,0xde,0xe7,0x99,0x73,0x57,0x63,0xba,0x92,0x9d,0x72,0x52,
  0x77,0x59,0x7,0x41,0xf6,0xd0,0x2c,0x45,0x29,0xd7,0x29,0xf2,0xae,0x4b,0xd8,0xa3,
  0x3d,0x96,0x97,0xdb,0x39,0xae,0x2f,0x9a,0xc4,0x3,0x41,0x2a,0x28,0x4d,0x5e,0x14,
  0x2f,0xb7,0x53,0xde,0xa2,0x6d,0x22,0x40,0x5d,0x19,0x4,0xa9,0x23,0x24,0x22,0x41,
  0x14,0xe7,0x8f,0xef,0xa6,0xb3,0x45,0xcc,0x54,0xaa,0x41,0xd8,0xcb,0x22,0x8,0xd2,
  0x94,0x94,0x88,0x84,0xc5,0xbc,0x2f,0xa7,0x5d,0x2f,0xcf,0xbb,0x44,0x54,0x1f,0x41,
  0x51,0x16,0xdf,0xb1,0x41,0x40,0x90,0x48,0x62,0x66,0xa7,0x5d,0x4c,0xa7,0x22,0x23,
  0xbd,0x2a,0x8e,0x20,0x49,0xd8,0x56,0xd3,0x2e,0x13,0xb,0x79,0xe7,0xef,0x1f,0xaf,
  0x5f,0x5d,0x25,0xe,0x73,0xf6,0xd5,0x10,0xa4,0xe5,0x29,0x30,0xde,0xf5,0x9,0xeb,
  0x8c,0x96,0xa1,0x25,0x83,0x74,0x1,0x70,0x73,0x8c,0x93,0xf,0xf,0xef,0x47,0xb3,
  0x2d,0xcc,0x74,0xaa,0xb3,0xd0,0x92,0x41,0x3a,0x43,0xb9,0x9e,0x76,0xf9,0xa3,0x2f,
  0xc3,0x2d,0xe2,0xc9,0x1a,0x1d,0x86,0x93,0xc,0xd2,0x35,0xcc,0x70,0xbc,0xc1,0xd6,
  0x26,0x64,0x8d,0x3e,0xc2,0xc9,0x22,0xbd,0x17,0xaa,0x1b,0x51,0x54,0xb2,0x9,0x59,
  0xa3,0xaf,0x18,0xb2,0x8b,0xd5,0x27,0x59,0x85,0x6c,0x12,0x9e,0xe2,0xfb,0xe7,0xed,
  0xd9,0x45,0xcf,0xc3,0x98,0xf5,0xe1,0x59,0x83,0x28,0x84,0xbf,0x97,0x5,0x3c,0x53,
  0x2a,0x85,0xc8,0x71,0x1d,0x44,0x5,0xf2,0xf7,0xb5,0x49,0x27,0x53,0x2e,0x9f,0x17,
  0xee,0xf9,0x62,0x6e,0xf,0x2e,0xa9,0x5,0x6a,0xa7,0x21,0x32,0x88,0x22,0xf9,0xb6,
  0xb,0x78,0xa6,0x54,0x8a,0xc1,0x5a,0x37,0x85,0x20,0xfa,0xcc,0x25,0x69,0xca,0xc5,
  0x94,0x6a,0x80,0x48,0x31,0xc5,0x1a,0x4,0x7a,0x68,0x34,0x46,0x92,0x36,0x6f,0x27,
  0x1f,0x6c,0x80,0x13,0x69,0x98,0xc,0x32,0x60,0x20,0xcb,0x29,0x57,0xe5,0xba,0x84,
  0x2d,0xdc,0x1,0xc3,0xc3,0x85,0xc2,0xa1,0xe1,0x57,0x2f,0xde,0x59,0x8c,0x8f,0x21,
  0x3e,0x64,0x90,0x11,0x44,0x61,0xf7,0x59,0x78,0x16,0xe3,0x23,0x8,0xa,0x8b,0xf4,
  0xf1,0x4,0xe1,0x7b,0x26,0x9,0xcf,0xc1,0x67,0xfe,0x94,0xdb,0xd3,0xc7,0x13,0x9b,
  0xff,0x3,0xbc,0x72,0x7e,0x32,0xc3,0xf,0xf6,0xca,0x0,0x0,0x0,0x0,0x49,0x45,
  0x4e,0x44,0xae,0x42,0x60,0x82,
  
};

static const unsigned char qt_resource_name[] = {
  // resources
  0x0,0x9,
  0xa,0x6c,0x78,0x43,
  0x0,0x72,
  0x0,0x65,0x0,0x73,0x0,0x6f,0x0,0x75,0x0,0x72,0x0,0x63,0x0,0x65,0x0,0x73,
    // favicon.png
  0x0,0xb,
  0xa,0xb8,0x4e,0xa7,
  0x0,0x66,
  0x0,0x61,0x0,0x76,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/resources
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/resources/favicon.png
  0x0,0x0,0x0,0x18,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x82,0xf1,0x9d,0x50,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_icon)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_icon)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icon)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icon)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_icon)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icon)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
