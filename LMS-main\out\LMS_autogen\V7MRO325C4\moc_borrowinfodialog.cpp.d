D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/V7MRO325C4/moc_borrowinfodialog.cpp: D:/course/DevOps/LMS-main20250704/LMS-main/include/library/borrowinfodialog.h \
  D:/course/DevOps/LMS-main20250704/LMS-main/out/LMS_autogen/moc_predefs.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q17memory.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20functional.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20iterator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20memory.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20type_traits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q20utility.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/q23utility.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qabstractitemmodel.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qanystringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydata.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydataops.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qarraydatapointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qassert.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qatomic_cxx11.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbasicatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbindingstorage.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearray.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearraylist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qbytearrayview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qchar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompare_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcomparehelpers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcompilerdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qconfig.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qconstructormacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerfwd.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainerinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontainertools_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qcontiguouscache.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdarwinhelpers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdatastream.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qdebug.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qendian.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qexceptionhandling.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qflags.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfloat16.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qforeach.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionaltools_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qfunctionpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qgenericatomic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qglobalstatic.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qhash.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qhashfunctions.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiodevicebase.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qitemselectionmodel.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterable.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qiterator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlatin1stringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qline.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlocale.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qlogging.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmalloc.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmargins.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmath.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetacontainer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qmetatype.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qminmax.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qnamespace.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qnumeric.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobject_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qobjectdefs_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qoverload.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qpair.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qpoint.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qprocessordetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qrect.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qrefcount.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qregularexpression.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qscopeguard.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qset.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qshareddata_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsharedpointer_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsize.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qspan.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstdlibdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstring.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringalgorithms.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringbuilder.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringconverter_base.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringfwd.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringlist.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringliteral.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringmatcher.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringtokenizer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qstringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qswap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsysinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qsystemdetection.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtaggedpointer.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtclasshelpermacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfiginclude.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtconfigmacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcore-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtcoreglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationdefinitions.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtdeprecationmarkers.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtenvironmentvariables.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtextstream.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtformat_impl.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtmetamacros.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtnoop.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtpreprocessorsupport.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtresource.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qttranslation.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qttypetraits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversion.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtversionchecks.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypeinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qtypes.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qutf8stringview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qvariant.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qvarlengtharray.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qversiontagging.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qxptype_traits.h \
  D:/QT/6.9.1/msvc2022_64/include/QtCore/qyieldcpu.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qaction.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qbitmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qbrush.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qcolor.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qcursor.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfont.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontinfo.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontmetrics.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qfontvariableaxis.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qicon.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qimage.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qkeysequence.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpaintdevice.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpalette.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixelformat.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpixmap.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qpolygon.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qregion.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgb.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qrgba64.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtgui-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtguiglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qtransform.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qvalidator.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs.h \
  D:/QT/6.9.1/msvc2022_64/include/QtGui/qwindowdefs_win.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QDialog \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/QTableWidget \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemdelegate.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractitemview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractscrollarea.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractslider.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qabstractspinbox.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qdialog.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qframe.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qrubberband.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qsizepolicy.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qslider.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyle.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qstyleoption.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabbar.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtableview.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtablewidget.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtabwidget.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgets-config.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsexports.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qtwidgetsglobal.h \
  D:/QT/6.9.1/msvc2022_64/include/QtWidgets/qwidget.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algo.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_algobase.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multiset.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_set.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_set.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/compare \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ranges \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/set \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_set \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdbool.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h \
  D:/tdm-gcc/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_mac.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_off_t.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_secapi.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_mingw_stat64.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/_timeval.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/assert.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_startup.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_stdio_config.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/corecrt_wstdlib.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/crtdefs.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/ctype.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/errno.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/locale.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/process.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/pthread.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_compat.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_signal.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_time.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/pthread_unistd.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/stdio_s.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/string_s.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sec_api/wchar_s.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/signal.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/stdio.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/string.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/swprintf.inl \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sys/timeb.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/sys/types.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/time.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/vadefs.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/wchar.h \
  D:/tdm-gcc/x86_64-w64-mingw32/include/wctype.h
