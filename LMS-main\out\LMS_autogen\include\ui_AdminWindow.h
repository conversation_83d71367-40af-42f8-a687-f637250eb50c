/********************************************************************************
** Form generated from reading UI file 'AdminWindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ADMINWINDOW_H
#define UI_ADMINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTableView>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_AdminWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *vboxLayout;
    QHBoxLayout *hboxLayout;
    QSpacerItem *horizontalSpacer;
    QLabel *welcomeLabel;
    QTabWidget *tabWidget;
    QWidget *tabBooks;
    QVBoxLayout *vboxLayout1;
    QTableView *tableViewBooks;
    QHBoxLayout *hboxLayout1;
    QComboBox *comboSearchBookType;
    QPushButton *btnAddBook;
    QPushButton *btnEditBook;
    QPushButton *btnDeleteBook;
    QPushButton *btnSearchBook;
    QWidget *tabUsers;
    QVBoxLayout *vboxLayout2;
    QTableView *tableViewUsers;
    QHBoxLayout *hboxLayout2;
    QComboBox *comboSearchUserType;
    QPushButton *btnAddUser;
    QPushButton *btnEditUser;
    QPushButton *btnDeleteUser;
    QPushButton *btnSearchUser;
    QPushButton *btnResetPassword;
    QPushButton *btnChangePassword;
    QPushButton *btnShowAllBooks;
    QPushButton *btnShowAllUsers;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *AdminWindow)
    {
        if (AdminWindow->objectName().isEmpty())
            AdminWindow->setObjectName("AdminWindow");
        AdminWindow->resize(1000, 600);
        centralwidget = new QWidget(AdminWindow);
        centralwidget->setObjectName("centralwidget");
        vboxLayout = new QVBoxLayout(centralwidget);
        vboxLayout->setObjectName("vboxLayout");
        hboxLayout = new QHBoxLayout();
        hboxLayout->setObjectName("hboxLayout");
        horizontalSpacer = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        hboxLayout->addItem(horizontalSpacer);

        welcomeLabel = new QLabel(centralwidget);
        welcomeLabel->setObjectName("welcomeLabel");

        hboxLayout->addWidget(welcomeLabel);


        vboxLayout->addLayout(hboxLayout);

        tabWidget = new QTabWidget(centralwidget);
        tabWidget->setObjectName("tabWidget");
        tabBooks = new QWidget();
        tabBooks->setObjectName("tabBooks");
        vboxLayout1 = new QVBoxLayout(tabBooks);
        vboxLayout1->setObjectName("vboxLayout1");
        tableViewBooks = new QTableView(tabBooks);
        tableViewBooks->setObjectName("tableViewBooks");

        vboxLayout1->addWidget(tableViewBooks);

        hboxLayout1 = new QHBoxLayout();
        hboxLayout1->setObjectName("hboxLayout1");
        comboSearchBookType = new QComboBox(tabBooks);
        comboSearchBookType->addItem(QString());
        comboSearchBookType->addItem(QString());
        comboSearchBookType->addItem(QString());
        comboSearchBookType->addItem(QString());
        comboSearchBookType->setObjectName("comboSearchBookType");

        hboxLayout1->addWidget(comboSearchBookType);

        btnAddBook = new QPushButton(tabBooks);
        btnAddBook->setObjectName("btnAddBook");

        hboxLayout1->addWidget(btnAddBook);

        btnEditBook = new QPushButton(tabBooks);
        btnEditBook->setObjectName("btnEditBook");

        hboxLayout1->addWidget(btnEditBook);

        btnDeleteBook = new QPushButton(tabBooks);
        btnDeleteBook->setObjectName("btnDeleteBook");

        hboxLayout1->addWidget(btnDeleteBook);

        btnSearchBook = new QPushButton(tabBooks);
        btnSearchBook->setObjectName("btnSearchBook");

        hboxLayout1->addWidget(btnSearchBook);


        vboxLayout1->addLayout(hboxLayout1);

        tabWidget->addTab(tabBooks, QString());
        tabUsers = new QWidget();
        tabUsers->setObjectName("tabUsers");
        vboxLayout2 = new QVBoxLayout(tabUsers);
        vboxLayout2->setObjectName("vboxLayout2");
        tableViewUsers = new QTableView(tabUsers);
        tableViewUsers->setObjectName("tableViewUsers");

        vboxLayout2->addWidget(tableViewUsers);

        hboxLayout2 = new QHBoxLayout();
        hboxLayout2->setObjectName("hboxLayout2");
        comboSearchUserType = new QComboBox(tabUsers);
        comboSearchUserType->addItem(QString());
        comboSearchUserType->addItem(QString());
        comboSearchUserType->setObjectName("comboSearchUserType");

        hboxLayout2->addWidget(comboSearchUserType);

        btnAddUser = new QPushButton(tabUsers);
        btnAddUser->setObjectName("btnAddUser");

        hboxLayout2->addWidget(btnAddUser);

        btnEditUser = new QPushButton(tabUsers);
        btnEditUser->setObjectName("btnEditUser");

        hboxLayout2->addWidget(btnEditUser);

        btnDeleteUser = new QPushButton(tabUsers);
        btnDeleteUser->setObjectName("btnDeleteUser");

        hboxLayout2->addWidget(btnDeleteUser);

        btnSearchUser = new QPushButton(tabUsers);
        btnSearchUser->setObjectName("btnSearchUser");

        hboxLayout2->addWidget(btnSearchUser);

        btnResetPassword = new QPushButton(tabUsers);
        btnResetPassword->setObjectName("btnResetPassword");

        hboxLayout2->addWidget(btnResetPassword);

        btnChangePassword = new QPushButton(tabUsers);
        btnChangePassword->setObjectName("btnChangePassword");

        hboxLayout2->addWidget(btnChangePassword);


        vboxLayout2->addLayout(hboxLayout2);

        tabWidget->addTab(tabUsers, QString());

        vboxLayout->addWidget(tabWidget);

        btnShowAllBooks = new QPushButton(centralwidget);
        btnShowAllBooks->setObjectName("btnShowAllBooks");

        vboxLayout->addWidget(btnShowAllBooks);

        btnShowAllUsers = new QPushButton(centralwidget);
        btnShowAllUsers->setObjectName("btnShowAllUsers");

        vboxLayout->addWidget(btnShowAllUsers);

        AdminWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(AdminWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1000, 18));
        AdminWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(AdminWindow);
        statusbar->setObjectName("statusbar");
        AdminWindow->setStatusBar(statusbar);

        retranslateUi(AdminWindow);

        tabWidget->setCurrentIndex(0);


        QMetaObject::connectSlotsByName(AdminWindow);
    } // setupUi

    void retranslateUi(QMainWindow *AdminWindow)
    {
        welcomeLabel->setText(QCoreApplication::translate("AdminWindow", "\344\275\240\345\245\275\357\274\214\347\256\241\347\220\206\345\221\230", nullptr));
        comboSearchBookType->setItemText(0, QCoreApplication::translate("AdminWindow", "\344\271\246\345\220\215", nullptr));
        comboSearchBookType->setItemText(1, QCoreApplication::translate("AdminWindow", "\344\275\234\350\200\205", nullptr));
        comboSearchBookType->setItemText(2, QCoreApplication::translate("AdminWindow", "\345\207\272\347\211\210\347\244\276", nullptr));
        comboSearchBookType->setItemText(3, QCoreApplication::translate("AdminWindow", "ISBN", nullptr));

        btnAddBook->setText(QCoreApplication::translate("AdminWindow", "\346\226\260\345\242\236", nullptr));
        btnEditBook->setText(QCoreApplication::translate("AdminWindow", "\347\274\226\350\276\221", nullptr));
        btnDeleteBook->setText(QCoreApplication::translate("AdminWindow", "\345\210\240\351\231\244", nullptr));
        btnSearchBook->setText(QCoreApplication::translate("AdminWindow", "\346\220\234\347\264\242", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tabBooks), QCoreApplication::translate("AdminWindow", "\344\271\246\347\261\215\347\256\241\347\220\206", nullptr));
        comboSearchUserType->setItemText(0, QCoreApplication::translate("AdminWindow", "ID", nullptr));
        comboSearchUserType->setItemText(1, QCoreApplication::translate("AdminWindow", "\345\247\223\345\220\215", nullptr));

        btnAddUser->setText(QCoreApplication::translate("AdminWindow", "\346\226\260\345\242\236", nullptr));
        btnEditUser->setText(QCoreApplication::translate("AdminWindow", "\347\274\226\350\276\221", nullptr));
        btnDeleteUser->setText(QCoreApplication::translate("AdminWindow", "\345\210\240\351\231\244", nullptr));
        btnSearchUser->setText(QCoreApplication::translate("AdminWindow", "\346\220\234\347\264\242", nullptr));
        btnResetPassword->setText(QCoreApplication::translate("AdminWindow", "\351\207\215\347\275\256\345\257\206\347\240\201", nullptr));
        btnChangePassword->setText(QCoreApplication::translate("AdminWindow", "\344\277\256\346\224\271\345\257\206\347\240\201", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(tabUsers), QCoreApplication::translate("AdminWindow", "\347\224\250\346\210\267\347\256\241\347\220\206", nullptr));
        btnShowAllBooks->setText(QCoreApplication::translate("AdminWindow", "\350\277\224\345\233\236\344\270\273\347\225\214\351\235\242\357\274\210\344\271\246\347\261\215\357\274\211", nullptr));
        btnShowAllUsers->setText(QCoreApplication::translate("AdminWindow", "\350\277\224\345\233\236\344\270\273\347\225\214\351\235\242\357\274\210\347\224\250\346\210\267\357\274\211", nullptr));
        (void)AdminWindow;
    } // retranslateUi

};

namespace Ui {
    class AdminWindow: public Ui_AdminWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ADMINWINDOW_H
