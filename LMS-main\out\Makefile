# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\course\DevOps\LMS-main20250704\LMS-main

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\course\DevOps\LMS-main20250704\LMS-main\out

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles D:\course\DevOps\LMS-main20250704\LMS-main\out\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\course\DevOps\LMS-main20250704\LMS-main\out\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named LMS

# Build rule for target.
LMS: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 LMS
.PHONY : LMS

# fast build rule for target.
LMS/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/build
.PHONY : LMS/fast

#=============================================================================
# Target rules for targets named LMS_autogen_timestamp_deps

# Build rule for target.
LMS_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 LMS_autogen_timestamp_deps
.PHONY : LMS_autogen_timestamp_deps

# fast build rule for target.
LMS_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen_timestamp_deps.dir\build.make CMakeFiles/LMS_autogen_timestamp_deps.dir/build
.PHONY : LMS_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named LMS_autogen

# Build rule for target.
LMS_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 LMS_autogen
.PHONY : LMS_autogen

# fast build rule for target.
LMS_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS_autogen.dir\build.make CMakeFiles/LMS_autogen.dir/build
.PHONY : LMS_autogen/fast

LMS_autogen/3YJK5W5UP7/qrc_icon.obj: LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.obj

# target to build an object file
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.obj

LMS_autogen/3YJK5W5UP7/qrc_icon.i: LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.i

# target to preprocess a source file
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.i

LMS_autogen/3YJK5W5UP7/qrc_icon.s: LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.s

# target to generate assembly for a file
LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s
.PHONY : LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.s

LMS_autogen/mocs_compilation.obj: LMS_autogen/mocs_compilation.cpp.obj
.PHONY : LMS_autogen/mocs_compilation.obj

# target to build an object file
LMS_autogen/mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.obj
.PHONY : LMS_autogen/mocs_compilation.cpp.obj

LMS_autogen/mocs_compilation.i: LMS_autogen/mocs_compilation.cpp.i
.PHONY : LMS_autogen/mocs_compilation.i

# target to preprocess a source file
LMS_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.i
.PHONY : LMS_autogen/mocs_compilation.cpp.i

LMS_autogen/mocs_compilation.s: LMS_autogen/mocs_compilation.cpp.s
.PHONY : LMS_autogen/mocs_compilation.s

# target to generate assembly for a file
LMS_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/LMS_autogen/mocs_compilation.cpp.s
.PHONY : LMS_autogen/mocs_compilation.cpp.s

src/Book.obj: src/Book.cpp.obj
.PHONY : src/Book.obj

# target to build an object file
src/Book.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Book.cpp.obj
.PHONY : src/Book.cpp.obj

src/Book.i: src/Book.cpp.i
.PHONY : src/Book.i

# target to preprocess a source file
src/Book.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Book.cpp.i
.PHONY : src/Book.cpp.i

src/Book.s: src/Book.cpp.s
.PHONY : src/Book.s

# target to generate assembly for a file
src/Book.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Book.cpp.s
.PHONY : src/Book.cpp.s

src/Library.obj: src/Library.cpp.obj
.PHONY : src/Library.obj

# target to build an object file
src/Library.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Library.cpp.obj
.PHONY : src/Library.cpp.obj

src/Library.i: src/Library.cpp.i
.PHONY : src/Library.i

# target to preprocess a source file
src/Library.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Library.cpp.i
.PHONY : src/Library.cpp.i

src/Library.s: src/Library.cpp.s
.PHONY : src/Library.s

# target to generate assembly for a file
src/Library.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/Library.cpp.s
.PHONY : src/Library.cpp.s

src/User.obj: src/User.cpp.obj
.PHONY : src/User.obj

# target to build an object file
src/User.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/User.cpp.obj
.PHONY : src/User.cpp.obj

src/User.i: src/User.cpp.i
.PHONY : src/User.i

# target to preprocess a source file
src/User.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/User.cpp.i
.PHONY : src/User.cpp.i

src/User.s: src/User.cpp.s
.PHONY : src/User.s

# target to generate assembly for a file
src/User.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/User.cpp.s
.PHONY : src/User.cpp.s

src/main.obj: src/main.cpp.obj
.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

util/CompressionUtil.obj: util/CompressionUtil.cpp.obj
.PHONY : util/CompressionUtil.obj

# target to build an object file
util/CompressionUtil.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/util/CompressionUtil.cpp.obj
.PHONY : util/CompressionUtil.cpp.obj

util/CompressionUtil.i: util/CompressionUtil.cpp.i
.PHONY : util/CompressionUtil.i

# target to preprocess a source file
util/CompressionUtil.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/util/CompressionUtil.cpp.i
.PHONY : util/CompressionUtil.cpp.i

util/CompressionUtil.s: util/CompressionUtil.cpp.s
.PHONY : util/CompressionUtil.s

# target to generate assembly for a file
util/CompressionUtil.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/util/CompressionUtil.cpp.s
.PHONY : util/CompressionUtil.cpp.s

window/RegisterWindow/registerwindow.obj: window/RegisterWindow/registerwindow.cpp.obj
.PHONY : window/RegisterWindow/registerwindow.obj

# target to build an object file
window/RegisterWindow/registerwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.obj
.PHONY : window/RegisterWindow/registerwindow.cpp.obj

window/RegisterWindow/registerwindow.i: window/RegisterWindow/registerwindow.cpp.i
.PHONY : window/RegisterWindow/registerwindow.i

# target to preprocess a source file
window/RegisterWindow/registerwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.i
.PHONY : window/RegisterWindow/registerwindow.cpp.i

window/RegisterWindow/registerwindow.s: window/RegisterWindow/registerwindow.cpp.s
.PHONY : window/RegisterWindow/registerwindow.s

# target to generate assembly for a file
window/RegisterWindow/registerwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/RegisterWindow/registerwindow.cpp.s
.PHONY : window/RegisterWindow/registerwindow.cpp.s

window/adminWindow/adminwindow.obj: window/adminWindow/adminwindow.cpp.obj
.PHONY : window/adminWindow/adminwindow.obj

# target to build an object file
window/adminWindow/adminwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.obj
.PHONY : window/adminWindow/adminwindow.cpp.obj

window/adminWindow/adminwindow.i: window/adminWindow/adminwindow.cpp.i
.PHONY : window/adminWindow/adminwindow.i

# target to preprocess a source file
window/adminWindow/adminwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.i
.PHONY : window/adminWindow/adminwindow.cpp.i

window/adminWindow/adminwindow.s: window/adminWindow/adminwindow.cpp.s
.PHONY : window/adminWindow/adminwindow.s

# target to generate assembly for a file
window/adminWindow/adminwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/adminWindow/adminwindow.cpp.s
.PHONY : window/adminWindow/adminwindow.cpp.s

window/bookEditDialog/bookeditdialog.obj: window/bookEditDialog/bookeditdialog.cpp.obj
.PHONY : window/bookEditDialog/bookeditdialog.obj

# target to build an object file
window/bookEditDialog/bookeditdialog.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.obj
.PHONY : window/bookEditDialog/bookeditdialog.cpp.obj

window/bookEditDialog/bookeditdialog.i: window/bookEditDialog/bookeditdialog.cpp.i
.PHONY : window/bookEditDialog/bookeditdialog.i

# target to preprocess a source file
window/bookEditDialog/bookeditdialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.i
.PHONY : window/bookEditDialog/bookeditdialog.cpp.i

window/bookEditDialog/bookeditdialog.s: window/bookEditDialog/bookeditdialog.cpp.s
.PHONY : window/bookEditDialog/bookeditdialog.s

# target to generate assembly for a file
window/bookEditDialog/bookeditdialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/bookEditDialog/bookeditdialog.cpp.s
.PHONY : window/bookEditDialog/bookeditdialog.cpp.s

window/borrowInfoDialog/borrowinfodialog.obj: window/borrowInfoDialog/borrowinfodialog.cpp.obj
.PHONY : window/borrowInfoDialog/borrowinfodialog.obj

# target to build an object file
window/borrowInfoDialog/borrowinfodialog.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.obj
.PHONY : window/borrowInfoDialog/borrowinfodialog.cpp.obj

window/borrowInfoDialog/borrowinfodialog.i: window/borrowInfoDialog/borrowinfodialog.cpp.i
.PHONY : window/borrowInfoDialog/borrowinfodialog.i

# target to preprocess a source file
window/borrowInfoDialog/borrowinfodialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.i
.PHONY : window/borrowInfoDialog/borrowinfodialog.cpp.i

window/borrowInfoDialog/borrowinfodialog.s: window/borrowInfoDialog/borrowinfodialog.cpp.s
.PHONY : window/borrowInfoDialog/borrowinfodialog.s

# target to generate assembly for a file
window/borrowInfoDialog/borrowinfodialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/borrowInfoDialog/borrowinfodialog.cpp.s
.PHONY : window/borrowInfoDialog/borrowinfodialog.cpp.s

window/forgotpasswordwindow/forgotpasswordwindow.obj: window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.obj

# target to build an object file
window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.cpp.obj

window/forgotpasswordwindow/forgotpasswordwindow.i: window/forgotpasswordwindow/forgotpasswordwindow.cpp.i
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.i

# target to preprocess a source file
window/forgotpasswordwindow/forgotpasswordwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.i
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.cpp.i

window/forgotpasswordwindow/forgotpasswordwindow.s: window/forgotpasswordwindow/forgotpasswordwindow.cpp.s
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.s

# target to generate assembly for a file
window/forgotpasswordwindow/forgotpasswordwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/forgotpasswordwindow/forgotpasswordwindow.cpp.s
.PHONY : window/forgotpasswordwindow/forgotpasswordwindow.cpp.s

window/loginWindow/loginwindow.obj: window/loginWindow/loginwindow.cpp.obj
.PHONY : window/loginWindow/loginwindow.obj

# target to build an object file
window/loginWindow/loginwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.obj
.PHONY : window/loginWindow/loginwindow.cpp.obj

window/loginWindow/loginwindow.i: window/loginWindow/loginwindow.cpp.i
.PHONY : window/loginWindow/loginwindow.i

# target to preprocess a source file
window/loginWindow/loginwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.i
.PHONY : window/loginWindow/loginwindow.cpp.i

window/loginWindow/loginwindow.s: window/loginWindow/loginwindow.cpp.s
.PHONY : window/loginWindow/loginwindow.s

# target to generate assembly for a file
window/loginWindow/loginwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/loginWindow/loginwindow.cpp.s
.PHONY : window/loginWindow/loginwindow.cpp.s

window/mainWindow/mainwindow.obj: window/mainWindow/mainwindow.cpp.obj
.PHONY : window/mainWindow/mainwindow.obj

# target to build an object file
window/mainWindow/mainwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.obj
.PHONY : window/mainWindow/mainwindow.cpp.obj

window/mainWindow/mainwindow.i: window/mainWindow/mainwindow.cpp.i
.PHONY : window/mainWindow/mainwindow.i

# target to preprocess a source file
window/mainWindow/mainwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.i
.PHONY : window/mainWindow/mainwindow.cpp.i

window/mainWindow/mainwindow.s: window/mainWindow/mainwindow.cpp.s
.PHONY : window/mainWindow/mainwindow.s

# target to generate assembly for a file
window/mainWindow/mainwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\LMS.dir\build.make CMakeFiles/LMS.dir/window/mainWindow/mainwindow.cpp.s
.PHONY : window/mainWindow/mainwindow.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... LMS_autogen
	@echo ... LMS_autogen_timestamp_deps
	@echo ... LMS
	@echo ... LMS_autogen/3YJK5W5UP7/qrc_icon.obj
	@echo ... LMS_autogen/3YJK5W5UP7/qrc_icon.i
	@echo ... LMS_autogen/3YJK5W5UP7/qrc_icon.s
	@echo ... LMS_autogen/mocs_compilation.obj
	@echo ... LMS_autogen/mocs_compilation.i
	@echo ... LMS_autogen/mocs_compilation.s
	@echo ... src/Book.obj
	@echo ... src/Book.i
	@echo ... src/Book.s
	@echo ... src/Library.obj
	@echo ... src/Library.i
	@echo ... src/Library.s
	@echo ... src/User.obj
	@echo ... src/User.i
	@echo ... src/User.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... util/CompressionUtil.obj
	@echo ... util/CompressionUtil.i
	@echo ... util/CompressionUtil.s
	@echo ... window/RegisterWindow/registerwindow.obj
	@echo ... window/RegisterWindow/registerwindow.i
	@echo ... window/RegisterWindow/registerwindow.s
	@echo ... window/adminWindow/adminwindow.obj
	@echo ... window/adminWindow/adminwindow.i
	@echo ... window/adminWindow/adminwindow.s
	@echo ... window/bookEditDialog/bookeditdialog.obj
	@echo ... window/bookEditDialog/bookeditdialog.i
	@echo ... window/bookEditDialog/bookeditdialog.s
	@echo ... window/borrowInfoDialog/borrowinfodialog.obj
	@echo ... window/borrowInfoDialog/borrowinfodialog.i
	@echo ... window/borrowInfoDialog/borrowinfodialog.s
	@echo ... window/forgotpasswordwindow/forgotpasswordwindow.obj
	@echo ... window/forgotpasswordwindow/forgotpasswordwindow.i
	@echo ... window/forgotpasswordwindow/forgotpasswordwindow.s
	@echo ... window/loginWindow/loginwindow.obj
	@echo ... window/loginWindow/loginwindow.i
	@echo ... window/loginWindow/loginwindow.s
	@echo ... window/mainWindow/mainwindow.obj
	@echo ... window/mainWindow/mainwindow.i
	@echo ... window/mainWindow/mainwindow.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

