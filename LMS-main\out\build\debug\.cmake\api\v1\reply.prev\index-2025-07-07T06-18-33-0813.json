{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6-msvc6", "suffix": "msvc6"}}, "objects": [{"jsonFile": "codemodel-v2-d325ea39cce7399ca493.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-1fd6f2ff48343c576363.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-491073399256119cafe3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-4ee8e1de79f4c11314fe.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-1fd6f2ff48343c576363.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-1fd6f2ff48343c576363.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-491073399256119cafe3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "codemodel-v2-d325ea39cce7399ca493.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-4ee8e1de79f4c11314fe.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-491073399256119cafe3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-d325ea39cce7399ca493.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}}}