{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "LMS", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "LMS::@6890427a1f51a3e7e1df", "jsonFile": "target-LMS-Debug-8515ec7399fbcc449ff7.json", "name": "LMS", "projectIndex": 0}, {"directoryIndex": 0, "id": "LMS_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-LMS_autogen-Debug-d46c26b6be13cb9506a9.json", "name": "LMS_autogen", "projectIndex": 0}, {"directoryIndex": 0, "id": "LMS_autogen_timestamp_deps::@6890427a1f51a3e7e1df", "jsonFile": "target-LMS_autogen_timestamp_deps-Debug-a14de28d37228e346d18.json", "name": "LMS_autogen_timestamp_deps", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/course/DevOps/LMS-main20250704/LMS-main/out/build/debug", "source": "D:/course/DevOps/LMS-main20250704/LMS-main"}, "version": {"major": 2, "minor": 8}}