{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Qt/Tools/CMake_64/bin/cmake.exe", "cpack": "C:/Qt/Tools/CMake_64/bin/cpack.exe", "ctest": "C:/Qt/Tools/CMake_64/bin/ctest.exe", "root": "C:/Qt/Tools/CMake_64/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-f44b40c79b1b8a3075d9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-425572926113aed8ac32.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e1b75578f7d2dfc08449.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-4ee8e1de79f4c11314fe.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-425572926113aed8ac32.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-425572926113aed8ac32.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e1b75578f7d2dfc08449.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "codemodel-v2-f44b40c79b1b8a3075d9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-4ee8e1de79f4c11314fe.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-e1b75578f7d2dfc08449.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-f44b40c79b1b8a3075d9.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}