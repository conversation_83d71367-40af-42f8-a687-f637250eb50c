{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "cpack": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cpack.exe", "ctest": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/ctest.exe", "root": "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5-msvc23", "suffix": "msvc23"}}, "objects": [{"jsonFile": "codemodel-v2-79cc6ea155afbfc1dbde.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-dcf1472d959192d3b1b2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-51190415f08b548ee3c9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-ee859490454f2f446423.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-MicrosoftVS": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "cmakeFiles", "version": 1}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}], "responses": [{"jsonFile": "cache-v2-dcf1472d959192d3b1b2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-51190415f08b548ee3c9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "codemodel-v2-79cc6ea155afbfc1dbde.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-ee859490454f2f446423.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}]}}}}