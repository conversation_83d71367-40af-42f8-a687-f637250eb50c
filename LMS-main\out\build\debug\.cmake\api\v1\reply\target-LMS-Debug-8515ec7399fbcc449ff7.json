{"artifacts": [{"path": "LMS.exe"}, {"path": "LMS.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["CMakeLists.txt", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "D:/vs/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "D:/QT/6.9.1/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 27, "parent": 0}, {"command": 1, "file": 0, "line": 78, "parent": 0}, {"command": 4, "file": 0, "line": 18, "parent": 0}, {"file": 3, "parent": 3}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 2, "parent": 5}, {"command": 3, "file": 2, "line": 55, "parent": 6}, {"file": 1, "parent": 7}, {"command": 2, "file": 1, "line": 61, "parent": 8}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 5, "parent": 10}, {"command": 3, "file": 5, "line": 57, "parent": 11}, {"file": 4, "parent": 12}, {"command": 2, "file": 4, "line": 61, "parent": 13}, {"command": 3, "file": 5, "line": 45, "parent": 11}, {"file": 10, "parent": 15}, {"command": 6, "file": 10, "line": 46, "parent": 16}, {"command": 5, "file": 9, "line": 137, "parent": 17}, {"command": 4, "file": 8, "line": 76, "parent": 18}, {"file": 7, "parent": 19}, {"command": 3, "file": 7, "line": 55, "parent": 20}, {"file": 6, "parent": 21}, {"command": 2, "file": 6, "line": 61, "parent": 22}, {"command": 2, "file": 6, "line": 83, "parent": 22}, {"command": 4, "file": 3, "line": 218, "parent": 4}, {"file": 12, "parent": 25}, {"command": 3, "file": 12, "line": 55, "parent": 26}, {"file": 11, "parent": 27}, {"command": 2, "file": 11, "line": 61, "parent": 28}, {"command": 7, "file": 0, "line": 13, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /Ob0 /Od /RTC1 -std:c++20 -MDd -Zi"}, {"backtrace": 2, "fragment": "-Zc:__cplusplus"}, {"backtrace": 2, "fragment": "-permissive-"}, {"backtrace": 2, "fragment": "-utf-8"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_WIDGETS_LIB"}, {"backtrace": 2, "define": "UNICODE"}, {"backtrace": 2, "define": "WIN32"}, {"backtrace": 2, "define": "WIN64"}, {"backtrace": 2, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 2, "define": "_UNICODE"}, {"backtrace": 2, "define": "_WIN64"}], "includes": [{"backtrace": 30, "path": "D:/course/DevOps/LMS-main20250704/LMS-main/include/library"}, {"backtrace": 0, "isSystem": true, "path": "D:/course/DevOps/LMS-main20250704/LMS-main/out/build/debug/LMS_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/include/QtGui"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/include/QtWidgets"}, {"backtrace": 2, "isSystem": true, "path": "D:/QT/6.9.1/msvc2022_64/include/QtNetwork"}], "language": "CXX", "languageStandard": {"backtraces": [2, 2], "standard": "20"}, "sourceIndexes": [0, 2, 3, 5, 7, 9, 12, 15, 18, 21, 24, 27, 30, 40]}], "dependencies": [{"id": "LMS_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}, {"backtrace": 0, "id": "LMS_autogen::@6890427a1f51a3e7e1df"}], "id": "LMS::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /Ob0 /Od /RTC1 -MDd -Zi", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:windows", "role": "flags"}, {"backtrace": 2, "fragment": "D:\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "D:\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6Networkd.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 9, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "D:\\QT\\6.9.1\\msvc2022_64\\lib\\Qt6EntryPointd.lib", "role": "libraries"}, {"backtrace": 24, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 29, "fragment": "ws2_32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "LMS", "nameOnDisk": "LMS.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 3, 5, 7, 9, 12, 15, 18, 21, 24, 27, 30, 40]}, {"name": "", "sourceIndexes": [1, 11, 14, 17, 20, 23, 26, 29, 39]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [4, 6, 8, 10, 13, 16, 19, 22, 25, 28, 31, 32, 33, 34, 35, 36, 37, 38]}, {"name": "CMake Rules", "sourceIndexes": [41, 42]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "resources/icon.qrc", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Book.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/Book.h", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/User.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/User.h", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/Library.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/Library.h", "sourceGroupIndex": 2}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/loginWindow/loginwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/loginwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/loginWindow/loginwindow.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/forgotpasswordwindow/forgotpasswordwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/forgotpasswordwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/forgotpasswordwindow/forgotpasswordwindow.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/RegisterWindow/RegisterWindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/RegisterWindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/RegisterWindow/RegisterWindow.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/mainWindow/mainwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/mainwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/mainWindow/mainwindow.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/adminWindow/adminwindow.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/adminwindow.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/adminWindow/adminwindow.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/borrowInfoDialog/borrowinfodialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/borrowinfodialog.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/borrowInfoDialog/borrowinfodialog.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "window/bookEditDialog/bookeditdialog.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/bookeditdialog.h", "sourceGroupIndex": 2}, {"backtrace": 1, "path": "window/bookEditDialog/bookeditdialog.ui", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "util/CompressionUtil.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/library/CompressionUtil.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/loginWindow/ui_loginwindow.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/forgotpasswordwindow/ui_forgotpasswordwindow.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/RegisterWindow/ui_RegisterWindow.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/mainWindow/ui_mainwindow.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/adminWindow/ui_adminwindow.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/borrowInfoDialog/ui_borrowinfodialog.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/include/window/bookEditDialog/ui_bookeditdialog.h", "sourceGroupIndex": 2}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/timestamp", "sourceGroupIndex": 1}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/timestamp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "out/build/debug/LMS_autogen/3YJK5W5UP7/qrc_icon.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}