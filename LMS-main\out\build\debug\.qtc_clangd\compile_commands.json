[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\src\\main.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/src/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\src\\Book.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/src/Book.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\src\\User.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/src/User.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\src\\Library.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/src/Library.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\loginWindow\\loginwindow.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/loginWindow/loginwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\forgotpasswordwindow\\forgotpasswordwindow.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/forgotpasswordwindow/forgotpasswordwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\RegisterWindow\\RegisterWindow.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/RegisterWindow/RegisterWindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\mainWindow\\mainwindow.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/mainWindow/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\adminWindow\\adminwindow.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/adminWindow/adminwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\borrowInfoDialog\\borrowinfodialog.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/borrowInfoDialog/borrowinfodialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\window\\bookEditDialog\\bookeditdialog.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/window/bookEditDialog/bookeditdialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\util\\CompressionUtil.cpp"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/util/CompressionUtil.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\Book.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/Book.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\User.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/User.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\Library.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/Library.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\loginwindow.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/loginwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\forgotpasswordwindow.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/forgotpasswordwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\RegisterWindow.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/RegisterWindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\mainwindow.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\adminwindow.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/adminwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\borrowinfodialog.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/borrowinfodialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\bookeditdialog.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/bookeditdialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-RTC1", "-clang:-std=c++20", "-MDd", "-<PERSON>i", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fms-compatibility-version=19.44", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "/clang:-isystem", "/clang:D:\\lib_copy\\LMS-main20250704\\LMS-main\\out\\build\\debug\\LMS_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\ATLMFC\\include", "/clang:-isystem", "/clang:D:\\Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\ucrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\um", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\shared", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\winrt", "/clang:-isystem", "/clang:D:\\Windows Kits\\10\\include\\10.0.26100.0\\cppwinrt", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library\\CompressionUtil.h"], "directory": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/.qtc_clangd", "file": "D:/lib_copy/LMS-main20250704/LMS-main/include/library/CompressionUtil.h"}]