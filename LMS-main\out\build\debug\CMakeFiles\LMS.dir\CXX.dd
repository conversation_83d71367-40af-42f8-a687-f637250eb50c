ninja_dyndep_version = 1.0
build CMakeFiles\LMS.dir\src\main.cpp.obj: dyndep

build CMakeFiles\LMS.dir\src\Book.cpp.obj: dyndep

build CMakeFiles\LMS.dir\src\User.cpp.obj: dyndep

build CMakeFiles\LMS.dir\src\Library.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\loginWindow\loginwindow.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\forgotpasswordwindow\forgotpasswordwindow.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\RegisterWindow\RegisterWindow.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\mainWindow\mainwindow.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\adminWindow\adminwindow.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\borrowInfoDialog\borrowinfodialog.cpp.obj: dyndep

build CMakeFiles\LMS.dir\window\bookEditDialog\bookeditdialog.cpp.obj: dyndep

build CMakeFiles\LMS.dir\util\CompressionUtil.cpp.obj: dyndep

