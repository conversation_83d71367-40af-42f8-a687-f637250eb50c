{"bmi-installation": null, "compiler-frontend-variant": "MSVC", "compiler-id": "MSVC", "compiler-simulate-id": "", "config": "Debug", "cxx-modules": {}, "dir-cur-bld": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug", "dir-cur-src": "D:/lib_copy/LMS-main20250704/LMS-main", "dir-top-bld": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug", "dir-top-src": "D:/lib_copy/LMS-main20250704/LMS-main", "exports": [], "forward-modules-from-target-dirs": [], "include-dirs": ["D:\\lib_copy\\LMS-main20250704\\LMS-main\\include\\library", "LMS_autogen\\include", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtCore", "C:\\Qt\\6.9.1\\msvc2022_64\\include", "C:\\Qt\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtGui", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtWidgets", "C:\\Qt\\6.9.1\\msvc2022_64\\include\\QtNetwork"], "language": "CXX", "linked-target-dirs": [], "module-dir": "D:/lib_copy/LMS-main20250704/LMS-main/out/build/debug/CMakeFiles/LMS.dir"}