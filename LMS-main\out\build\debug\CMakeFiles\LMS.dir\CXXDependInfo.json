{"bmi-installation": null, "compiler-frontend-variant": "MSVC", "compiler-id": "MSVC", "compiler-simulate-id": "", "config": "Debug", "cxx-modules": {}, "dir-cur-bld": "D:/course/DevOps/LMS-main20250704/LMS-main/out/build/debug", "dir-cur-src": "D:/course/DevOps/LMS-main20250704/LMS-main", "dir-top-bld": "D:/course/DevOps/LMS-main20250704/LMS-main/out/build/debug", "dir-top-src": "D:/course/DevOps/LMS-main20250704/LMS-main", "exports": [], "forward-modules-from-target-dirs": [], "include-dirs": ["D:\\course\\DevOps\\LMS-main20250704\\LMS-main\\include\\library", "LMS_autogen\\include", "D:\\QT\\6.9.1\\msvc2022_64\\include\\QtCore", "D:\\QT\\6.9.1\\msvc2022_64\\include", "D:\\QT\\6.9.1\\msvc2022_64\\mkspecs\\win32-msvc", "D:\\QT\\6.9.1\\msvc2022_64\\include\\QtGui", "D:\\QT\\6.9.1\\msvc2022_64\\include\\QtWidgets", "D:\\QT\\6.9.1\\msvc2022_64\\include\\QtNetwork"], "language": "CXX", "linked-target-dirs": [], "module-dir": "D:/course/DevOps/LMS-main20250704/LMS-main/out/build/debug/CMakeFiles/LMS.dir"}