/********************************************************************************
** Form generated from reading UI file 'borrowinfodialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_BORROWINFODIALOG_H
#define UI_BORROWINFODIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAbstractButton>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDialogButtonBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_BorrowInfoDialog
{
public:
    QVBoxLayout *verticalLayout;
    QTableWidget *tableWidget;
    QDialogButtonBox *buttonBox;

    void setupUi(QDialog *BorrowInfoDialog)
    {
        if (BorrowInfoDialog->objectName().isEmpty())
            BorrowInfoDialog->setObjectName("BorrowInfoDialog");
        BorrowInfoDialog->resize(400, 300);
        verticalLayout = new QVBoxLayout(BorrowInfoDialog);
        verticalLayout->setObjectName("verticalLayout");
        tableWidget = new QTableWidget(BorrowInfoDialog);
        tableWidget->setObjectName("tableWidget");

        verticalLayout->addWidget(tableWidget);

        buttonBox = new QDialogButtonBox(BorrowInfoDialog);
        buttonBox->setObjectName("buttonBox");
        buttonBox->setStandardButtons(QDialogButtonBox::Close);

        verticalLayout->addWidget(buttonBox);


        retranslateUi(BorrowInfoDialog);

        QMetaObject::connectSlotsByName(BorrowInfoDialog);
    } // setupUi

    void retranslateUi(QDialog *BorrowInfoDialog)
    {
        BorrowInfoDialog->setWindowTitle(QCoreApplication::translate("BorrowInfoDialog", "\345\200\237\351\230\205\344\277\241\346\201\257", nullptr));
    } // retranslateUi

};

namespace Ui {
    class BorrowInfoDialog: public Ui_BorrowInfoDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_BORROWINFODIALOG_H
