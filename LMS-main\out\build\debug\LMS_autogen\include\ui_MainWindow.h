/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *topLayout;
    QSpacerItem *horizontalSpacer;
    QLabel *welcomeLabel;
    QHBoxLayout *searchLayout;
    QLineEdit *searchEdit;
    QComboBox *searchTypeCombo;
    QPushButton *searchButton;
    QPushButton *changePasswordButton;
    QTableWidget *tableWidget;
    QHBoxLayout *borrowReturnLayout;
    QPushButton *borrowButton;
    QPushButton *returnButton;
    QPushButton *viewBorrowedButton;
    QPushButton *returnMainButton;
    QLabel *label;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(1000, 600);
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        verticalLayout = new QVBoxLayout(centralwidget);
        verticalLayout->setObjectName("verticalLayout");
        topLayout = new QHBoxLayout();
        topLayout->setObjectName("topLayout");
        horizontalSpacer = new QSpacerItem(0, 0, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        topLayout->addItem(horizontalSpacer);

        welcomeLabel = new QLabel(centralwidget);
        welcomeLabel->setObjectName("welcomeLabel");

        topLayout->addWidget(welcomeLabel);


        verticalLayout->addLayout(topLayout);

        searchLayout = new QHBoxLayout();
        searchLayout->setObjectName("searchLayout");
        searchEdit = new QLineEdit(centralwidget);
        searchEdit->setObjectName("searchEdit");

        searchLayout->addWidget(searchEdit);

        searchTypeCombo = new QComboBox(centralwidget);
        searchTypeCombo->addItem(QString());
        searchTypeCombo->addItem(QString());
        searchTypeCombo->addItem(QString());
        searchTypeCombo->setObjectName("searchTypeCombo");

        searchLayout->addWidget(searchTypeCombo);

        searchButton = new QPushButton(centralwidget);
        searchButton->setObjectName("searchButton");

        searchLayout->addWidget(searchButton);


        verticalLayout->addLayout(searchLayout);

        changePasswordButton = new QPushButton(centralwidget);
        changePasswordButton->setObjectName("changePasswordButton");

        verticalLayout->addWidget(changePasswordButton);

        tableWidget = new QTableWidget(centralwidget);
        if (tableWidget->columnCount() < 8)
            tableWidget->setColumnCount(8);
        QTableWidgetItem *__qtablewidgetitem = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(0, __qtablewidgetitem);
        QTableWidgetItem *__qtablewidgetitem1 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(1, __qtablewidgetitem1);
        QTableWidgetItem *__qtablewidgetitem2 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(2, __qtablewidgetitem2);
        QTableWidgetItem *__qtablewidgetitem3 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(3, __qtablewidgetitem3);
        QTableWidgetItem *__qtablewidgetitem4 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(4, __qtablewidgetitem4);
        QTableWidgetItem *__qtablewidgetitem5 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(5, __qtablewidgetitem5);
        QTableWidgetItem *__qtablewidgetitem6 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(6, __qtablewidgetitem6);
        QTableWidgetItem *__qtablewidgetitem7 = new QTableWidgetItem();
        tableWidget->setHorizontalHeaderItem(7, __qtablewidgetitem7);
        tableWidget->setObjectName("tableWidget");
        tableWidget->setColumnCount(8);
        tableWidget->horizontalHeader()->setDefaultSectionSize(120);

        verticalLayout->addWidget(tableWidget);

        borrowReturnLayout = new QHBoxLayout();
        borrowReturnLayout->setObjectName("borrowReturnLayout");
        borrowButton = new QPushButton(centralwidget);
        borrowButton->setObjectName("borrowButton");

        borrowReturnLayout->addWidget(borrowButton);

        returnButton = new QPushButton(centralwidget);
        returnButton->setObjectName("returnButton");

        borrowReturnLayout->addWidget(returnButton);

        viewBorrowedButton = new QPushButton(centralwidget);
        viewBorrowedButton->setObjectName("viewBorrowedButton");

        borrowReturnLayout->addWidget(viewBorrowedButton);


        verticalLayout->addLayout(borrowReturnLayout);

        returnMainButton = new QPushButton(centralwidget);
        returnMainButton->setObjectName("returnMainButton");

        verticalLayout->addWidget(returnMainButton);

        label = new QLabel(centralwidget);
        label->setObjectName("label");

        verticalLayout->addWidget(label);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 1000, 25));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "Library", nullptr));
        welcomeLabel->setText(QCoreApplication::translate("MainWindow", "\344\275\240\345\245\275\357\274\214\347\224\250\346\210\267", nullptr));
        searchEdit->setPlaceholderText(QCoreApplication::translate("MainWindow", "Enter keyword...", nullptr));
        searchTypeCombo->setItemText(0, QCoreApplication::translate("MainWindow", "\344\271\246\345\220\215", nullptr));
        searchTypeCombo->setItemText(1, QCoreApplication::translate("MainWindow", "\344\275\234\350\200\205", nullptr));
        searchTypeCombo->setItemText(2, QCoreApplication::translate("MainWindow", "ISBN", nullptr));

        searchButton->setText(QCoreApplication::translate("MainWindow", "Search", nullptr));
        changePasswordButton->setText(QCoreApplication::translate("MainWindow", "Change Password", nullptr));
        QTableWidgetItem *___qtablewidgetitem = tableWidget->horizontalHeaderItem(0);
        ___qtablewidgetitem->setText(QCoreApplication::translate("MainWindow", "\344\271\246\345\220\215", nullptr));
        QTableWidgetItem *___qtablewidgetitem1 = tableWidget->horizontalHeaderItem(1);
        ___qtablewidgetitem1->setText(QCoreApplication::translate("MainWindow", "\344\275\234\350\200\205", nullptr));
        QTableWidgetItem *___qtablewidgetitem2 = tableWidget->horizontalHeaderItem(2);
        ___qtablewidgetitem2->setText(QCoreApplication::translate("MainWindow", "\345\207\272\347\211\210\347\244\276", nullptr));
        QTableWidgetItem *___qtablewidgetitem3 = tableWidget->horizontalHeaderItem(3);
        ___qtablewidgetitem3->setText(QCoreApplication::translate("MainWindow", "\345\207\272\347\211\210\345\271\264\344\273\275", nullptr));
        QTableWidgetItem *___qtablewidgetitem4 = tableWidget->horizontalHeaderItem(4);
        ___qtablewidgetitem4->setText(QCoreApplication::translate("MainWindow", "ISBN", nullptr));
        QTableWidgetItem *___qtablewidgetitem5 = tableWidget->horizontalHeaderItem(5);
        ___qtablewidgetitem5->setText(QCoreApplication::translate("MainWindow", "\345\217\257\347\224\250\346\225\260\351\207\217", nullptr));
        QTableWidgetItem *___qtablewidgetitem6 = tableWidget->horizontalHeaderItem(6);
        ___qtablewidgetitem6->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\345\267\262\345\200\237\351\230\205", nullptr));
        QTableWidgetItem *___qtablewidgetitem7 = tableWidget->horizontalHeaderItem(7);
        ___qtablewidgetitem7->setText(QCoreApplication::translate("MainWindow", "\345\211\251\344\275\231\346\227\266\351\227\264", nullptr));
        borrowButton->setText(QCoreApplication::translate("MainWindow", "\345\200\237\351\230\205", nullptr));
        returnButton->setText(QCoreApplication::translate("MainWindow", "\345\275\222\350\277\230", nullptr));
        viewBorrowedButton->setText(QCoreApplication::translate("MainWindow", "\346\237\245\347\234\213\345\267\262\345\200\237\351\230\205\345\233\276\344\271\246", nullptr));
        returnMainButton->setText(QCoreApplication::translate("MainWindow", "\350\277\224\345\233\236\344\270\273\347\225\214\351\235\242", nullptr));
        label->setText(QCoreApplication::translate("MainWindow", "\345\246\202\351\201\207\345\210\260\345\233\260\351\232\276\357\274\214\350\257\267\350\201\224\347\263\273\347\256\241\347\220\206\345\221\230  <EMAIL>", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
