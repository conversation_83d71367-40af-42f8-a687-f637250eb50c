/********************************************************************************
** Form generated from reading UI file 'bookeditdialog.ui'
**
** Created by: Qt User Interface Compiler version 6.9.1
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_BOOKEDITDIALOG_H
#define UI_BOOKEDITDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_BookEditDialog
{
public:
    QVBoxLayout *verticalLayout;
    QWidget *formWidget;
    QFormLayout *formLayout;
    QLabel *labelTitle;
    QLineEdit *lineEditTitle;
    QLabel *labelAuthor;
    QLineEdit *lineEditAuthor;
    QLabel *labelPublisher;
    QLineEdit *lineEditPublisher;
    QLabel *labelYear;
    QLineEdit *lineEditYear;
    QLabel *labelISBN;
    QLineEdit *lineEditISBN;
    QLabel *labelTotal;
    QLineEdit *lineEditTotal;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *buttonBoxOk;
    QPushButton *buttonBoxCancel;

    void setupUi(QDialog *BookEditDialog)
    {
        if (BookEditDialog->objectName().isEmpty())
            BookEditDialog->setObjectName("BookEditDialog");
        verticalLayout = new QVBoxLayout(BookEditDialog);
        verticalLayout->setObjectName("verticalLayout");
        formWidget = new QWidget(BookEditDialog);
        formWidget->setObjectName("formWidget");
        formLayout = new QFormLayout(formWidget);
        formLayout->setObjectName("formLayout");
        formLayout->setLabelAlignment(Qt::AlignRight|Qt::AlignVCenter);
        formLayout->setFormAlignment(Qt::AlignLeft|Qt::AlignTop);
        formLayout->setContentsMargins(0, 0, 0, 0);
        labelTitle = new QLabel(formWidget);
        labelTitle->setObjectName("labelTitle");

        formLayout->setWidget(0, QFormLayout::ItemRole::LabelRole, labelTitle);

        lineEditTitle = new QLineEdit(formWidget);
        lineEditTitle->setObjectName("lineEditTitle");

        formLayout->setWidget(0, QFormLayout::ItemRole::FieldRole, lineEditTitle);

        labelAuthor = new QLabel(formWidget);
        labelAuthor->setObjectName("labelAuthor");

        formLayout->setWidget(1, QFormLayout::ItemRole::LabelRole, labelAuthor);

        lineEditAuthor = new QLineEdit(formWidget);
        lineEditAuthor->setObjectName("lineEditAuthor");

        formLayout->setWidget(1, QFormLayout::ItemRole::FieldRole, lineEditAuthor);

        labelPublisher = new QLabel(formWidget);
        labelPublisher->setObjectName("labelPublisher");

        formLayout->setWidget(2, QFormLayout::ItemRole::LabelRole, labelPublisher);

        lineEditPublisher = new QLineEdit(formWidget);
        lineEditPublisher->setObjectName("lineEditPublisher");

        formLayout->setWidget(2, QFormLayout::ItemRole::FieldRole, lineEditPublisher);

        labelYear = new QLabel(formWidget);
        labelYear->setObjectName("labelYear");

        formLayout->setWidget(3, QFormLayout::ItemRole::LabelRole, labelYear);

        lineEditYear = new QLineEdit(formWidget);
        lineEditYear->setObjectName("lineEditYear");

        formLayout->setWidget(3, QFormLayout::ItemRole::FieldRole, lineEditYear);

        labelISBN = new QLabel(formWidget);
        labelISBN->setObjectName("labelISBN");

        formLayout->setWidget(4, QFormLayout::ItemRole::LabelRole, labelISBN);

        lineEditISBN = new QLineEdit(formWidget);
        lineEditISBN->setObjectName("lineEditISBN");

        formLayout->setWidget(4, QFormLayout::ItemRole::FieldRole, lineEditISBN);

        labelTotal = new QLabel(formWidget);
        labelTotal->setObjectName("labelTotal");

        formLayout->setWidget(5, QFormLayout::ItemRole::LabelRole, labelTotal);

        lineEditTotal = new QLineEdit(formWidget);
        lineEditTotal->setObjectName("lineEditTotal");

        formLayout->setWidget(5, QFormLayout::ItemRole::FieldRole, lineEditTotal);


        verticalLayout->addWidget(formWidget);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName("buttonLayout");
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        buttonBoxOk = new QPushButton(BookEditDialog);
        buttonBoxOk->setObjectName("buttonBoxOk");
        buttonBoxOk->setAutoDefault(false);

        buttonLayout->addWidget(buttonBoxOk);

        buttonBoxCancel = new QPushButton(BookEditDialog);
        buttonBoxCancel->setObjectName("buttonBoxCancel");
        buttonBoxCancel->setAutoDefault(false);

        buttonLayout->addWidget(buttonBoxCancel);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(BookEditDialog);
        QObject::connect(buttonBoxOk, &QPushButton::clicked, BookEditDialog, qOverload<>(&QDialog::accept));
        QObject::connect(buttonBoxCancel, &QPushButton::clicked, BookEditDialog, qOverload<>(&QDialog::reject));

        buttonBoxOk->setDefault(true);


        QMetaObject::connectSlotsByName(BookEditDialog);
    } // setupUi

    void retranslateUi(QDialog *BookEditDialog)
    {
        BookEditDialog->setWindowTitle(QCoreApplication::translate("BookEditDialog", "\345\233\276\344\271\246\344\277\241\346\201\257", nullptr));
        labelTitle->setText(QCoreApplication::translate("BookEditDialog", "\344\271\246\345\220\215:", nullptr));
        labelAuthor->setText(QCoreApplication::translate("BookEditDialog", "\344\275\234\350\200\205:", nullptr));
        labelPublisher->setText(QCoreApplication::translate("BookEditDialog", "\345\207\272\347\211\210\347\244\276:", nullptr));
        labelYear->setText(QCoreApplication::translate("BookEditDialog", "\345\207\272\347\211\210\345\271\264\344\273\275:", nullptr));
        labelISBN->setText(QCoreApplication::translate("BookEditDialog", "ISBN:", nullptr));
        labelTotal->setText(QCoreApplication::translate("BookEditDialog", "\346\200\273\345\272\223\345\255\230:", nullptr));
        buttonBoxOk->setText(QCoreApplication::translate("BookEditDialog", "\347\241\256\350\256\244", nullptr));
        buttonBoxCancel->setText(QCoreApplication::translate("BookEditDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class BookEditDialog: public Ui_BookEditDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_BOOKEDITDIALOG_H
