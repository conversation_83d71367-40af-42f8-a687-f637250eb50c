#include "Library.h"
#include "User.h"
#include <QFile>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QMessageBox>
#include <algorithm>
#include <stdexcept>

Library::Library(const std::filesystem::path& userPath, const std::filesystem::path& bookPath)
    : user_data_file_path(userPath), book_data_file_path(bookPath) {
    loadFromFile(userPath, bookPath);
}

void Library::setDataFilePaths(const std::filesystem::path& userPath, const std::filesystem::path& bookPath) {
    user_data_file_path = userPath;
    book_data_file_path = bookPath;
}

std::vector<std::filesystem::path> Library::getDataFilePaths() {
    return { user_data_file_path, book_data_file_path };
}

bool Library::registerUser(const User& user) {
    if (std::ranges::any_of(users,
                            [&user](const User& u) { return u.getId() == user.getId(); })) {
        return false;
    }
    users.push_back(user);
    saveToFile(user_data_file_path, book_data_file_path);
    return true;
}

bool Library::deleteUser(long id) {
    const auto it = std::ranges::remove_if(users,
                                           [id](const User& u) { return u.getId() == id; }).begin();
    if (it != users.end()) {
        users.erase(it, users.end());
        saveToFile(user_data_file_path, book_data_file_path);
        return true;
    }
    return false;
}

bool Library::updateUser(const User& updatedUser) {
    for (auto& user : users) {
        if (user.getId() == updatedUser.getId()) {
            user = updatedUser;
            saveToFile(user_data_file_path, book_data_file_path);
            return true;
        }
    }
    return false;
}

bool Library::addUser(const User& user) {
    for (const auto& u : users) {
        if (u.getId() == user.getId()) {
            return false;
        }
    }
    users.push_back(user);
    saveToFile(user_data_file_path, book_data_file_path);
    return true;
}

User* Library::loginUser(const long id, const std::string_view password) {
    for (auto& user : users) {
        if (user.getId() == id && user.checkPassword(password))
            return &user;
    }
    return nullptr;
}

User* Library::findUserById(const long long id) {
    for (auto& user : users) {
        if (user.getId() == id)
            return &user;
    }
    return nullptr;
}

const std::vector<User>& Library::getAllUsers() const {
    return users;
}

bool Library::addBook(const Book& book) {
    if (!book.isValidISBN()) {
        qWarning() << "无效ISBN，添加失败！";
        return false;
    }
    if (std::ranges::any_of(books,
                            [&book](const Book& b) { return b.getISBN() == book.getISBN(); })) {
        return false;
    }
    books.push_back(book);
    saveToFile(user_data_file_path, book_data_file_path);
    return true;
}

bool Library::deleteBook(const QString& ISBN) {
    auto it = std::ranges::remove_if(books,
                                     [&ISBN](const Book& b) { return b.getISBN() == ISBN; }).begin();
    if (it != books.end()) {
        books.erase(it, books.end());
        saveToFile(user_data_file_path, book_data_file_path);
        return true;
    }
    return false;
}

bool Library::updateBook(const Book& updatedBook) {
    for (auto& book : books) {
        if (book.getISBN() == updatedBook.getISBN()) {
            book = updatedBook;
            saveToFile(user_data_file_path, book_data_file_path);
            return true;
        }
    }
    return false;
}

Book* Library::findBookByISBN(const QString& ISBN) {
    for (auto& book : books) {
        if (book.getISBN() == ISBN)
            return &book;
    }
    return nullptr;
}

const std::vector<Book>& Library::getAllBooks() const {
    return books;
}

std::vector<Book> Library::searchBooksByISBN(const std::string_view keyword) const {
    std::vector<Book> results;
    for (const auto& book : books) {
        if (std::string(book.getISBN().toStdString()).find(keyword) != std::string::npos) {
            results.push_back(book);
        }
    }
    return results;
}

std::vector<Book> Library::searchBooksByTitle(const std::string_view keyword) const {
    if (keyword.empty()) return books;
    std::vector<Book> results;
    for (const auto& book : books) {
        if (std::string(book.getTitle()).find(keyword) != std::string::npos)
            results.push_back(book);
    }
    return results;
}

std::vector<Book> Library::searchBooksByAuthor(const std::string_view keyword) const {
    std::vector<Book> results;
    for (const auto& book : books) {
        if (std::string(book.getAuthor()).find(keyword) != std::string::npos)
            results.push_back(book);
    }
    return results;
}

std::vector<Book> Library::searchBooksByPublisher(const std::string_view keyword) const {
    std::vector<Book> results;
    for (const auto& book : books) {
        if (std::string(book.getPublisher()).find(keyword) != std::string::npos)
            results.push_back(book);
    }
    return results;
}

bool Library::borrowBook(long long userId, const QString& ISBN) {
    User* user = findUserById(userId);
    Book* book = findBookByISBN(ISBN);
    if (!user || !book || book->getAvailableCopies() <= 0)
        return false;
    if (!user->borrowBook(ISBN))
        return false;
    book->decreaseAvailableCopies();
    saveToFile(user_data_file_path, book_data_file_path);
    return true;
}

bool Library::returnBook(long long userId, const QString& ISBN) {
    User* user = findUserById(userId);
    Book* book = findBookByISBN(ISBN);
    if (!user || !book)
        return false;
    if (!user->returnBook(ISBN))
        return false;
    book->increaseAvailableCopies();
    saveToFile(user_data_file_path, book_data_file_path);
    return true;
}

bool Library::isBookBorrowedByUser(long long userId, const std::string& isbn) {
    auto user = findUserById(userId);
    if (!user) return false;
    const auto& borrowedBooks = user->getBorrowedBooks();
    return std::find(borrowedBooks.begin(), borrowedBooks.end(), isbn) != borrowedBooks.end();
}

void Library::saveToFile(const std::filesystem::path& userFile, const std::filesystem::path& bookFile) const {
    QJsonObject fullJson = toJson();
    QFile file(QString::fromStdString(userFile.string()));
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "无法保存用户数据:" << QString::fromStdString(userFile.string());
        return;
    }
    QJsonDocument doc(fullJson);
    file.write(doc.toJson(QJsonDocument::Indented));
    file.close();
}

void Library::loadFromFile(const std::filesystem::path& userFile, const std::filesystem::path& bookFile) {
    QFile file(QString::fromStdString(userFile.string()));
    if (!file.exists()) {
        User adminUser("Admin", "PowerCode", 100, Group::Admin);
        users.push_back(adminUser);
        saveToFile(userFile, bookFile);
        return;
    }

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "无法读取用户数据:" << QString::fromStdString(userFile.string());
        return;
    }

    QByteArray rawData = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(rawData);
    file.close();

    if (!doc.isNull() && doc.isObject()) {
        fromJson(doc.object());
    }

    bool hasAdmin = std::ranges::any_of(users, [](const User& u) {
        return u.getGroup() == Group::Admin;
    });

    if (!hasAdmin) {
        qWarning() << "未找到管理员用户，自动添加默认管理员账号 Admin";
        users.push_back(User("Admin", "PowerCode", 100, Group::Admin));
        if (currentId <= 100) currentId = 101;
        saveToFile(userFile, bookFile);
    }
}

std::vector<Book> Library::getBooksBorrowedByUser(long long userId) {
    std::vector<Book> result;
    const auto user = findUserById(userId);
    if (!user) return result;

    const auto& borrowedISBNs = user->getBorrowedBooks();
    for (const auto& isbn : borrowedISBNs) {
        if (const auto book = findBookByISBN(isbn)) {
            result.push_back(*book);
        }
    }
    return result;
}

long Library::generateUniqueId() {
    return currentId++;
}

QJsonObject Library::toJson() const {
    QJsonObject obj;

    QJsonArray booksArray;
    for (const auto& book : books) {
        booksArray.append(book.toJson());
    }
    obj["books"] = booksArray;

    QJsonArray usersArray;
    for (const auto& user : users) {
        usersArray.append(user.toJson());
    }
    obj["users"] = usersArray;

    obj["currentId"] = static_cast<qint64>(currentId);
    return obj;
}

void Library::fromJson(const QJsonObject& obj) {
    books.clear();
    QJsonArray booksArray = obj["books"].toArray();
    for (const auto& bookValue : booksArray) {
        Book book;
        book.fromJson(bookValue.toObject());
        books.push_back(book);
    }

    users.clear();
    QJsonArray usersArray = obj["users"].toArray();
    for (const auto& userValue : usersArray) {
        User user;
        user.fromJson(userValue.toObject());
        users.push_back(user);
    }

    currentId = obj["currentId"].toVariant().toLongLong();
}
