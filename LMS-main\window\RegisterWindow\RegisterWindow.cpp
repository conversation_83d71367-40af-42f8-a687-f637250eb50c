#include "RegisterWindow.h"
#include <QMessageBox>
#include <QVBoxLayout>
#include <QLineEdit>
#include <QPushButton>
#include <QRandomGenerator>
#include "Library.h"

#include <QJsonObject>           // 构造 JSON
#include <QJsonDocument>         // JSON 转 QByteArray
#include <QNetworkRequest>       // 网络请求
#include <QNetworkReply>         // 网络响应
#include <QUrl>                  // URL

RegisterWindow::RegisterWindow(Library* library, LoginWindow* loginWindow, QWidget* parent)
    : QDialog(parent), library(library), loginWindow(loginWindow) {

    usernameEdit = new QLineEdit(this);
    usernameEdit->setPlaceholderText("用户名");
    passwordEdit = new QLineEdit(this);
    passwordEdit->setPlaceholderText("密码");
    passwordEdit->setEchoMode(QLineEdit::Password);

    answerEdit = new QLineEdit(this);
    answerEdit->setPlaceholderText("你妈妈叫什么？（用于找回密码）");
    answerEdit->setEchoMode(QLineEdit::Normal);

    registerButton = new QPushButton("注册", this);
    exitButton = new QPushButton("退出", this);

    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->addWidget(usernameEdit);
    layout->addWidget(passwordEdit);
    layout->addWidget(answerEdit);
    layout->addWidget(registerButton);
    layout->addWidget(exitButton);

    setLayout(layout);

    connect(registerButton, &QPushButton::clicked, this, &RegisterWindow::onRegisterClicked);
    connect(exitButton, &QPushButton::clicked, this, &RegisterWindow::onExitClicked);

    // 初始化网络管理器，连接响应槽
    networkManager = new QNetworkAccessManager(this);
    connect(networkManager, &QNetworkAccessManager::finished, this, &RegisterWindow::onNetworkReplyFinished);
}

RegisterWindow::~RegisterWindow() {
    // networkManager 会自动删除（QObject 父子机制）
}

void RegisterWindow::onRegisterClicked() {
    QString name = usernameEdit->text();
    QString password = passwordEdit->text();
    QString securityAnswer = answerEdit->text();

    if (name.isEmpty() || password.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "用户名和密码不能为空！");
        return;
    }

    if (securityAnswer.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "密保答案不能为空！");
        return;
    }

    // 构造 JSON 请求体，默认 group 为 User
    QJsonObject jsonObj;
    jsonObj["name"] = name;
    jsonObj["password"] = password;
    jsonObj["securityAnswer"] = securityAnswer;
    jsonObj["group"] = "User";

    QJsonDocument jsonDoc(jsonObj);
    QByteArray jsonData = jsonDoc.toJson();

    // 构造请求，注意修改 URL 为你的后端地址和端口
    QNetworkRequest request(QUrl("http://localhost:8080/api/v1/user"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 发送 POST 请求
    networkManager->post(request, jsonData);
}

// 网络请求结束后调用
void RegisterWindow::onNetworkReplyFinished(QNetworkReply* reply) {
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray respData = reply->readAll();
        QJsonDocument respJson = QJsonDocument::fromJson(respData);
        if (respJson.isObject()) {
            QJsonObject obj = respJson.object();
            // 从后端返回构造 User
            User newUser;
            newUser.fromJson(obj);  // 直接用服务器返回的 JSON 初始化


            library->addUser(newUser);

            QMessageBox::information(this, "注册成功", "用户注册成功！您的ID是：" + QString::number(obj["id"].toInt()));
            if (loginWindow) {
                loginWindow->show();
            }
            this->close();
        } else {
            QMessageBox::warning(this, "注册失败", "服务器返回格式错误");
        }
    } else {
        QMessageBox::warning(this, "注册失败", "错误: " + reply->errorString());
    }
    reply->deleteLater();
}

void RegisterWindow::onExitClicked() {
    if (loginWindow) loginWindow->show();
    this->close();
}

int RegisterWindow::generateUniqueId() {
    return QRandomGenerator::global()->bounded(100, 999);
}
