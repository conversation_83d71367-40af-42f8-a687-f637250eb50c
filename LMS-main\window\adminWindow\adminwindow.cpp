#include "AdminWindow.h"
#include "ui_AdminWindow.h"
#include "BorrowInfoDialog.h"
#include "bookeditdialog.h"
#include "RegisterWindow.h"

#include <QMessageBox>
#include <QInputDialog>
#include <QContextMenuEvent>
#include <QRegularExpression>
#include <QMenu>
#include <QVBoxLayout>

#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrl>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonParseError>
#include <QStandardItemModel>

#include <QUrlQuery>
#include <QDebug>  // 添加调试输出

AdminWindow::AdminWindow(Library* library, User* user, QWidget* parent)
    : QMainWindow(parent), ui(new Ui::AdminWindow), library(library), currentUser(user),
    bookModel(new QStandardItemModel(this)), userModel(new QStandardItemModel(this)) {
    const QIcon icon(":/resources/favicon.png");
    this->setWindowIcon(icon);
    ui->setupUi(this);

    if (currentUser) {
        ui->welcomeLabel->setText(QString("你好，%1").arg(currentUser->getName()));
    }

    setupBookTable();
    setupUserTable();
    refreshBookTable();
    refreshUserTable();

    // 图书按钮连接
    connect(ui->btnAddBook, &QPushButton::clicked, this, &AdminWindow::onAddBook);
    connect(ui->btnEditBook, &QPushButton::clicked, this, &AdminWindow::onEditBook);
    connect(ui->btnDeleteBook, &QPushButton::clicked, this, &AdminWindow::onDeleteBook);
    connect(ui->btnSearchBook, &QPushButton::clicked, this, &AdminWindow::onSearchBook);
    connect(ui->btnShowAllBooks, &QPushButton::clicked, this, &AdminWindow::onShowAllBooksClicked);

    //管理员密码修改链接
    connect(ui->btnChangePassword, &QPushButton::clicked, this, &AdminWindow::onChangePasswordClicked);
    // 用户按钮连接
    connect(ui->btnAddUser, &QPushButton::clicked, this, &AdminWindow::onAddUser);
    connect(ui->btnEditUser, &QPushButton::clicked, this, &AdminWindow::onEditUser);
    connect(ui->btnDeleteUser, &QPushButton::clicked, this, &AdminWindow::onDeleteUser);
    connect(ui->btnSearchUser, &QPushButton::clicked, this, &AdminWindow::onSearchUser);
    connect(ui->btnResetPassword, &QPushButton::clicked, this, &AdminWindow::onResetPasswordClicked);
     connect(ui->btnShowAllUsers, &QPushButton::clicked, this, &AdminWindow::onShowAllUsersClicked);
}

AdminWindow::~AdminWindow() {
    delete ui;
}

void AdminWindow::trySaveData() {
    try {
        library->saveToFile(library->getDataFilePaths()[0], library->getDataFilePaths()[1]);
    }
    catch (const std::exception& e) {
        QMessageBox::critical(this, "保存失败", e.what());
    }
}

void AdminWindow::setupBookTable() const {
    bookModel->setHorizontalHeaderLabels({ "书名", "作者", "出版社", "出版年份", "ISBN", "可用", "总库存" });
    ui->tableViewBooks->setModel(bookModel);
    ui->tableViewBooks->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
}

void AdminWindow::setupUserTable() const {
    userModel->setHorizontalHeaderLabels({ "ID", "姓名", "用户组", "借阅数量" });
    ui->tableViewUsers->setModel(userModel);
    ui->tableViewUsers->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
}

void AdminWindow::refreshBookTable() const {
    bookModel->removeRows(0, bookModel->rowCount());
    for (const auto& book : library->getAllBooks()) {
        QList<QStandardItem*> row;
        row << new QStandardItem(book.getTitle())
            << new QStandardItem(book.getAuthor())
            << new QStandardItem(book.getPublisher())
            << new QStandardItem(QString::number(book.getPublishYear()))
            << new QStandardItem(book.getISBN())
            << new QStandardItem(QString::number(book.getAvailableCopies()))
            << new QStandardItem(QString::number(book.getTotalCopies()));
        bookModel->appendRow(row);
    }
}

void AdminWindow::refreshUserTable() const {
    userModel->removeRows(0, userModel->rowCount());
    for (const auto& user : library->getAllUsers()) {
        QString group = user.getGroup() == Group::Admin ? "Admin" : "User";
        userModel->appendRow({
            new QStandardItem(QString::number(user.getId())),
            new QStandardItem(user.getName()),
            new QStandardItem(group),
            new QStandardItem(QString::number(user.getBorrowedBooks().size()))
            });
    }
}

bool AdminWindow::isValidISBN(const QString& isbn) {
    // 简单校验：10位或13位数字，10位最后允许是X
    const static QRegularExpression re(R"(^(\d{10}|\d{9}[Xx]|\d{13})$)");
    return re.match(isbn).hasMatch();
}

// =================== 图书操作 ===================

void AdminWindow::onAddBook() {
    BookEditDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        QString isbnStr = dialog.getISBN();
        if (!isValidISBN(isbnStr)) {
            QMessageBox::warning(this, "错误", "ISBN格式不正确");
            return;
        }

        // 构造请求数据
        QJsonObject bookData;
        bookData["title"] = dialog.getTitle();
        bookData["author"] = dialog.getAuthor();
        bookData["publisher"] = dialog.getPublisher();
        bookData["publishYear"] = static_cast<int>(dialog.getYear());
        bookData["ISBN"] = isbnStr;
        bookData["totalCopies"] = dialog.getTotal();

        QUrl url("http://127.0.0.1:8080/api/v1/book");
        QNetworkRequest request(url);
        request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

        QNetworkAccessManager* manager = new QNetworkAccessManager(this);
        QNetworkReply* reply = manager->post(request, QJsonDocument(bookData).toJson());

        connect(reply, &QNetworkReply::finished, this, [=]() {
            reply->deleteLater();
            manager->deleteLater();

            if (reply->error() != QNetworkReply::NoError) {
                QMessageBox::critical(this, "错误", "无法连接后端服务器！");
                return;
            }

            int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
            QByteArray response = reply->readAll();
            QJsonParseError parseError;
            QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

            if (parseError.error != QJsonParseError::NoError) {
                QMessageBox::critical(this, "错误", "返回数据格式有误！");
                return;
            }

            QJsonObject res = doc.object();
            if (statusCode == 201) {
                // 后端创建成功，同步到本地
                Book newBook;
                newBook.fromJson(res);
                if (!library->addBook(newBook)) {
                    QMessageBox::warning(this, "警告", "后端成功但本地添加失败（可能ISBN冲突）");
                } else {
                    trySaveData();
                }
                QMessageBox::information(this, "成功", "书籍添加成功！");
                onShowAllBooksClicked(); // 刷新书籍列表
            } else if (statusCode == 409) {
                QMessageBox::warning(this, "错误", "ISBN已存在！");
            } else if (statusCode == 400) {
                QMessageBox::warning(this, "错误", "输入数据无效！");
            } else {
                QString errorMsg = res.contains("error") ? res["error"].toString() : "未知错误";
                QMessageBox::warning(this, "错误", QString("添加失败：%1").arg(errorMsg));
            }
        });
    }
}

void AdminWindow::onShowAllBooksClicked() {
    QUrl url("http://127.0.0.1:8080/api/v1/book");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->get(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = reply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError || !doc.isArray()) {
            QMessageBox::critical(this, "错误", "返回数据格式有误！");
            return;
        }

        QJsonArray books = doc.array();

        // 刷新书籍表格
        bookModel->removeRows(0, bookModel->rowCount());

        for (const auto& bookValue : books) {
            QJsonObject bookObj = bookValue.toObject();
            QList<QStandardItem*> row;
            row << new QStandardItem(bookObj["title"].toString())
                << new QStandardItem(bookObj["author"].toString())
                << new QStandardItem(bookObj["publisher"].toString())
                << new QStandardItem(QString::number(bookObj["publishYear"].toInt()))
                << new QStandardItem(bookObj["ISBN"].toString())
                << new QStandardItem(QString::number(bookObj["availableCopies"].toInt()))
                << new QStandardItem(QString::number(bookObj["totalCopies"].toInt()));
            bookModel->appendRow(row);

            // 同步到本地Library
            Book book;
            book.fromJson(bookObj);
            // 检查本地是否已存在，如果不存在则添加
            if (!library->findBookByISBN(book.getISBN())) {
                library->addBook(book);
            }
        }
        trySaveData();
    });
}


void AdminWindow::onEditBook() {
    const QModelIndex index = ui->tableViewBooks->currentIndex();
    if (!index.isValid()) return;

    QString isbnStr = bookModel->item(index.row(), 4)->text();

    // 首先从后端获取书籍ID
    QUrl getUrl(QString("http://127.0.0.1:8080/api/v1/book"));
    QNetworkRequest getRequest(getUrl);
    getRequest.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* getManager = new QNetworkAccessManager(this);
    QNetworkReply* getReply = getManager->get(getRequest);

    connect(getReply, &QNetworkReply::finished, this, [=]() {
        getReply->deleteLater();
        getManager->deleteLater();

        if (getReply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = getReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError || !doc.isArray()) {
            QMessageBox::critical(this, "错误", "返回数据格式有误！");
            return;
        }

        QJsonArray books = doc.array();
        int bookId = -1;
        QJsonObject currentBookData;

        // 查找对应的书籍ID
        for (const auto& bookValue : books) {
            QJsonObject bookObj = bookValue.toObject();
            if (bookObj["ISBN"].toString() == isbnStr) {
                bookId = bookObj["id"].toInt();
                currentBookData = bookObj;
                break;
            }
        }

        if (bookId == -1) {
            QMessageBox::warning(this, "错误", "未找到该书籍！");
            return;
        }

        // 打开编辑对话框
        BookEditDialog dialog(this);
        dialog.setBookInfo(currentBookData["title"].toString(),
                          currentBookData["author"].toString(),
                          currentBookData["publisher"].toString(),
                          currentBookData["publishYear"].toInt(),
                          currentBookData["ISBN"].toString(),
                          currentBookData["totalCopies"].toInt());

        if (dialog.exec() == QDialog::Accepted) {
            QString newIsbnStr = dialog.getISBN();
            if (!isValidISBN(newIsbnStr)) {
                QMessageBox::warning(this, "错误", "ISBN格式不正确");
                return;
            }

            // 构造更新请求数据
            QJsonObject updateData;
            updateData["title"] = dialog.getTitle();
            updateData["author"] = dialog.getAuthor();
            updateData["publisher"] = dialog.getPublisher();
            updateData["publishYear"] = static_cast<int>(dialog.getYear());
            updateData["ISBN"] = newIsbnStr;
            updateData["totalCopies"] = dialog.getTotal();

            // 发送PUT请求更新书籍
            QUrl updateUrl(QString("http://127.0.0.1:8080/api/v1/book/%1").arg(bookId));
            QNetworkRequest updateRequest(updateUrl);
            updateRequest.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

            QNetworkAccessManager* updateManager = new QNetworkAccessManager(this);
            QNetworkReply* updateReply = updateManager->put(updateRequest, QJsonDocument(updateData).toJson());

            connect(updateReply, &QNetworkReply::finished, this, [=]() {
                updateReply->deleteLater();
                updateManager->deleteLater();

                if (updateReply->error() != QNetworkReply::NoError) {
                    QMessageBox::critical(this, "错误", "无法连接后端服务器！");
                    return;
                }

                int statusCode = updateReply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
                QByteArray updateResponse = updateReply->readAll();
                QJsonParseError updateParseError;
                QJsonDocument updateDoc = QJsonDocument::fromJson(updateResponse, &updateParseError);

                if (updateParseError.error != QJsonParseError::NoError) {
                    QMessageBox::critical(this, "错误", "返回数据格式有误！");
                    return;
                }

                QJsonObject res = updateDoc.object();
                if (statusCode == 200) {
                    // 后端更新成功，同步到本地
                    Book* localBook = library->findBookByISBN(isbnStr);
                    if (localBook) {
                        localBook->setTitle(dialog.getTitle().toStdString());
                        localBook->setAuthor(dialog.getAuthor().toStdString());
                        localBook->setPublisher(dialog.getPublisher().toStdString());
                        localBook->setPublishYear(static_cast<int>(dialog.getYear()));
                        localBook->setISBN(newIsbnStr);
                        localBook->setTotalCopies(dialog.getTotal());
                        trySaveData();
                    }
                    QMessageBox::information(this, "成功", "书籍更新成功！");
                    onShowAllBooksClicked(); // 刷新书籍列表
                } else if (statusCode == 409) {
                    QMessageBox::warning(this, "错误", "ISBN已被其他书籍使用！");
                } else if (statusCode == 404) {
                    QMessageBox::warning(this, "错误", "未找到该书籍！");
                } else {
                    QString errorMsg = res.contains("error") ? res["error"].toString() : "未知错误";
                    QMessageBox::warning(this, "错误", QString("更新失败：%1").arg(errorMsg));
                }
            });
        }
    });
}

void AdminWindow::onDeleteBook() const {
    const QModelIndex index = ui->tableViewBooks->currentIndex();
    if (!index.isValid()) return;

    QString isbnStr = bookModel->item(index.row(), 4)->text();

    // 首先从后端获取书籍ID
    QUrl getUrl("http://127.0.0.1:8080/api/v1/book");
    QNetworkRequest getRequest(getUrl);
    getRequest.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* getManager = new QNetworkAccessManager(const_cast<AdminWindow*>(this));
    QNetworkReply* getReply = getManager->get(getRequest);

    connect(getReply, &QNetworkReply::finished, const_cast<AdminWindow*>(this), [=]() {
        getReply->deleteLater();
        getManager->deleteLater();

        if (getReply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(const_cast<AdminWindow*>(this), "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = getReply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError || !doc.isArray()) {
            QMessageBox::critical(const_cast<AdminWindow*>(this), "错误", "返回数据格式有误！");
            return;
        }

        QJsonArray books = doc.array();
        int bookId = -1;

        // 查找对应的书籍ID
        for (const auto& bookValue : books) {
            QJsonObject bookObj = bookValue.toObject();
            if (bookObj["ISBN"].toString() == isbnStr) {
                bookId = bookObj["id"].toInt();
                break;
            }
        }

        if (bookId == -1) {
            QMessageBox::warning(const_cast<AdminWindow*>(this), "错误", "未找到该书籍！");
            return;
        }

        // 确认删除
        int ret = QMessageBox::question(const_cast<AdminWindow*>(this), "确认删除",
                                       QString("确定要删除ISBN为 %1 的书籍吗？").arg(isbnStr),
                                       QMessageBox::Yes | QMessageBox::No);
        if (ret != QMessageBox::Yes) {
            return;
        }

        // 发送DELETE请求
        QUrl deleteUrl(QString("http://127.0.0.1:8080/api/v1/book/%1").arg(bookId));
        QNetworkRequest deleteRequest(deleteUrl);
        deleteRequest.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

        QNetworkAccessManager* deleteManager = new QNetworkAccessManager(const_cast<AdminWindow*>(this));
        QNetworkReply* deleteReply = deleteManager->deleteResource(deleteRequest);

        connect(deleteReply, &QNetworkReply::finished, const_cast<AdminWindow*>(this), [=]() {
            deleteReply->deleteLater();
            deleteManager->deleteLater();

            if (deleteReply->error() != QNetworkReply::NoError) {
                QMessageBox::critical(const_cast<AdminWindow*>(this), "错误", "无法连接后端服务器！");
                return;
            }

            int statusCode = deleteReply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

            if (statusCode == 204) {
                // 后端删除成功，同步到本地
                library->deleteBook(isbnStr);
                const_cast<AdminWindow*>(this)->trySaveData();
                QMessageBox::information(const_cast<AdminWindow*>(this), "成功", "书籍删除成功！");
                const_cast<AdminWindow*>(this)->onShowAllBooksClicked(); // 刷新书籍列表
            } else if (statusCode == 409) {
                QMessageBox::warning(const_cast<AdminWindow*>(this), "错误", "无法删除已被借阅的书籍！");
            } else if (statusCode == 404) {
                QMessageBox::warning(const_cast<AdminWindow*>(this), "错误", "未找到该书籍！");
            } else {
                QByteArray errorResponse = deleteReply->readAll();
                QJsonParseError errorParseError;
                QJsonDocument errorDoc = QJsonDocument::fromJson(errorResponse, &errorParseError);
                QString errorMsg = "未知错误";
                if (errorParseError.error == QJsonParseError::NoError) {
                    QJsonObject errorObj = errorDoc.object();
                    if (errorObj.contains("error")) {
                        errorMsg = errorObj["error"].toString();
                    }
                }
                QMessageBox::warning(const_cast<AdminWindow*>(this), "错误", QString("删除失败：%1").arg(errorMsg));
            }
        });
    });
}

void AdminWindow::onSearchBook() {
    const QString keyword = QInputDialog::getText(this, "搜索图书", "输入关键字:");

    if (keyword.trimmed().isEmpty()) {
        // 如果关键字为空，显示所有书籍
        onShowAllBooksClicked();
        return;
    }

    // 使用后端搜索API
    QUrl url("http://127.0.0.1:8080/api/v1/book/search");
    QUrlQuery query;
    query.addQueryItem("q", keyword);
    url.setQuery(query);

    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->get(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = reply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError || !doc.isArray()) {
            QMessageBox::critical(this, "错误", "返回数据格式有误！");
            return;
        }

        QJsonArray books = doc.array();

        // 清空并填充搜索结果
        bookModel->removeRows(0, bookModel->rowCount());

        if (books.isEmpty()) {
            QMessageBox::information(this, "搜索结果", "未找到匹配的书籍！");
            return;
        }

        for (const auto& bookValue : books) {
            QJsonObject bookObj = bookValue.toObject();
            QList<QStandardItem*> row;
            row << new QStandardItem(bookObj["title"].toString())
                << new QStandardItem(bookObj["author"].toString())
                << new QStandardItem(bookObj["publisher"].toString())
                << new QStandardItem(QString::number(bookObj["publishYear"].toInt()))
                << new QStandardItem(bookObj["ISBN"].toString())
                << new QStandardItem(QString::number(bookObj["availableCopies"].toInt()))
                << new QStandardItem(QString::number(bookObj["totalCopies"].toInt()));
            bookModel->appendRow(row);
        }

        QMessageBox::information(this, "搜索结果", QString("找到 %1 本匹配的书籍").arg(books.size()));
    });
}

// =================== 用户操作 ===================

void AdminWindow::onShowAllUsersClicked() {
    QUrl url("http://127.0.0.1:8080/api/v1/user");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->get(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = reply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError || !doc.isArray()) {
            QMessageBox::critical(this, "错误", "返回数据格式有误！");
            return;
        }

        QJsonArray users = doc.array();

        // ✅ 刷新用户表格
        userModel->removeRows(0, userModel->rowCount());

        bool hasCurrentUser = false;

        for (const QJsonValue& val : users) {
            QJsonObject userObj = val.toObject();

            int id = userObj["id"].toInt();
            QString name = userObj["name"].toString();
            QString group = userObj["group"].toString();
            int borrowCount = userObj["borrowedBooks"].toArray().size();

            if (currentUser && id == currentUser->getId())
                hasCurrentUser = true;

            userModel->appendRow({
                new QStandardItem(QString::number(id)),
                new QStandardItem(name),
                new QStandardItem(group),
                new QStandardItem(QString::number(borrowCount))
                });
        }

        // ✅ 如果后端不包含当前登录用户（如默认 admin），手动添加
        if (currentUser && !hasCurrentUser) {
            QString groupStr = currentUser->getGroup() == Group::Admin ? "Admin" : "User";
            userModel->appendRow({
                new QStandardItem(QString::number(currentUser->getId())),
                new QStandardItem(currentUser->getName()),
                new QStandardItem(groupStr),
                new QStandardItem(QString::number(currentUser->getBorrowedBooks().size()))
                });
        }
        });
}


void AdminWindow::onResetPasswordClicked() {
    const QModelIndex index = ui->tableViewUsers->currentIndex();
    if (!index.isValid()) {
        QMessageBox::warning(this, "提示", "请先选中一个用户！");
        return;
    }

    const long long userId = userModel->item(index.row(), 0)->text().toLongLong();

    if (QMessageBox::question(this, "确认", "确定要将该用户密码重置？") != QMessageBox::Yes)
        return;

    // ✅ 正确构造本地 URL
    QUrl url(QString("http://127.0.0.1:8080/api/v1/user/%1/reset-password").arg(userId));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->post(request, QByteArray()); // 空 body

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "请求失败", "无法连接本地后端，请确认后端是否在运行！");
            return;
        }

        QByteArray responseData = reply->readAll();
        QJsonParseError jsonError;
        QJsonDocument doc = QJsonDocument::fromJson(responseData, &jsonError);
        if (jsonError.error != QJsonParseError::NoError) {
            QMessageBox::critical(this, "错误", "返回数据解析失败！");
            return;
        }

        QJsonObject obj = doc.object();
        if (obj.contains("new_password")) {
            QString newPwd = obj["new_password"].toString();

            // ✅ 1. 更新前端用户对象（内存中的 user）
            User* user = library->findUserById(userId);
            if (user) {
                user->setPassword(newPwd.toStdString());  // 假设你有 setPassword 方法
            }

            // ✅ 2. 弹窗提示新密码
            QMessageBox::information(this, "密码已重置", "新密码为：" + newPwd);

            // ✅ 3. 保存本地数据（可选）
            trySaveData();
        }

        else if (obj.contains("error")) {
            QMessageBox::warning(this, "重置失败", obj["error"].toString());
        }
        else {
            QMessageBox::information(this, "提示", "重置完成，但没有返回新密码。");
        }
        });
}


void AdminWindow::contextMenuEvent(QContextMenuEvent* event) {
    QPoint pos = ui->tableViewUsers->viewport()->mapFromGlobal(event->globalPos());
    QModelIndex index = ui->tableViewUsers->indexAt(pos);
    if (!index.isValid()) {
        QMainWindow::contextMenuEvent(event);
        return;
    }

    QMenu menu(this);
    QAction* viewBorrowInfoAction = menu.addAction("查看借阅信息");

    connect(viewBorrowInfoAction, &QAction::triggered, [this, index]() {
        long long userId = userModel->item(index.row(), 0)->text().toLongLong();
        showUserBorrowInfoDialog(userId);
        });

    menu.exec(event->globalPos());
}

void AdminWindow::showUserBorrowInfoDialog(long long userId) {
    const User* user = library->findUserById(userId);
    if (!user) {
        QMessageBox::warning(this, "错误", "未找到该用户");
        return;
    }

    std::vector<BorrowEntry> borrowList;
    for (const auto& isbn : user->getBorrowedBooks()) {
        if (auto borrowTimeOpt = user->getBorrowTime(isbn)) {
            borrowList.emplace_back(isbn, *borrowTimeOpt);
        }
    }

    BorrowInfoDialog dialog(this);
    dialog.setBorrowList(borrowList);
    dialog.exec();
}

void AdminWindow::onAddUser() {
    bool ok;

    // 不让用户输入ID，去掉ID输入
    QString name = QInputDialog::getText(this, "新建用户", "姓名:", QLineEdit::Normal, "", &ok);
    if (!ok || name.isEmpty()) return;

    QStringList groupOptions = { "User", "Admin" };
    QString groupStr = QInputDialog::getItem(this, "新建用户", "选择用户组:", groupOptions, 0, false, &ok);
    if (!ok || groupStr.isEmpty()) return;

    QString securityAnswer = QInputDialog::getText(this, "密保问题", "你母亲的名字是：", QLineEdit::Normal, "", &ok);
    if (!ok || securityAnswer.isEmpty()) return;

    // 构造 JSON，不包含 id 字段
    QJsonObject userData;
    userData["name"] = name;
    userData["group"] = groupStr;
    userData["password"] = "123456";  // 默认密码
    userData["securityAnswer"] = securityAnswer;

    QUrl url("http://127.0.0.1:8080/api/v1/user");
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->post(request, QJsonDocument(userData).toJson());

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        QByteArray response = reply->readAll();
        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(response, &err);

        if (err.error != QJsonParseError::NoError) {
            QMessageBox::critical(this, "错误", "返回数据解析失败！");
            return;
        }

        QJsonObject res = doc.object();

        if (res.contains("error")) {
            QMessageBox::warning(this, "失败", res["error"].toString());
            return;
        }

        // 这里后端返回完整用户信息，包括自动生成的 id
        int id = res["id"].toInt();
        QString nameFromServer = res["name"].toString();
        QString groupFromServer = res["group"].toString();
        QString pwdFromServer = res["password"].toString();
        QString securityAnswerFromServer = res["securityAnswer"].toString();

        Group groupEnum = (groupFromServer == "Admin") ? Group::Admin : Group::User;

        // 用后端返回的id构造User
        User newUser(nameFromServer.toStdString(), pwdFromServer.toStdString(), id, groupEnum);
        newUser.setSecurityAnswer(securityAnswerFromServer);

        if (!library->registerUser(newUser)) {
            QMessageBox::warning(this, "警告", "后端成功但本地添加失败（可能 ID 冲突）");
        }
        else {
            trySaveData();
        }

        QMessageBox::information(this, "成功",
            QString("用户创建成功（初始密码为123456）\n用户ID为：%1").arg(id));

        onShowAllUsersClicked(); // 刷新前端表格
        });
}

void AdminWindow::onEditUser() {
    const QModelIndex index = ui->tableViewUsers->currentIndex();
    if (!index.isValid()) return;

    const int uid = userModel->item(index.row(), 0)->text().toInt();
    QString currentName = userModel->item(index.row(), 1)->text();
    QString currentGroup = userModel->item(index.row(), 2)->text();

    // 1. 获取新用户名
    bool ok;
    const QString newName = QInputDialog::getText(this, "编辑用户", "新姓名:", QLineEdit::Normal, currentName, &ok);
    if (!ok || newName.trimmed().isEmpty()) return;

    // 2. 获取新分组
    const QStringList groupOptions = { "User", "Admin" };
    const QString newGroup = QInputDialog::getItem(this, "编辑用户", "新用户组:", groupOptions,
        (currentGroup == "Admin") ? 1 : 0, false, &ok);
    if (!ok || newGroup.isEmpty()) return;

    // 3. 构造请求数据
    QJsonObject jsonObj;
    jsonObj["name"] = newName;
    jsonObj["group"] = newGroup;
    QJsonDocument doc(jsonObj);
    QByteArray jsonData = doc.toJson();

    // 4. 构建 PUT 请求
    QUrl url(QString("http://localhost:8080/api/v1/user/%1").arg(uid));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->put(request, jsonData);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "请求失败", reply->errorString());
            return;
        }

        int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
        if (statusCode == 200) {
            QMessageBox::information(this, "成功", "用户信息已更新");

            // ✅ 同步更新本地对象（library）
            User* user = library->findUserById(uid);
            if (user) {
                user->setName(newName.toStdString());
                Group groupEnum = (newGroup == "Admin") ? Group::Admin : Group::User;
                user->setGroup(groupEnum);
            }

            // ✅ 保存到本地文件（同步存储）
            trySaveData();

            // ✅ 刷新表格
            refreshUserTable();

        }
        else if (statusCode == 409) {
            QMessageBox::warning(this, "错误", "用户名已被其他用户使用！");
        }
        else if (statusCode == 404) {
            QMessageBox::warning(this, "错误", "未找到该用户！");
        }
        else {
            QMessageBox::warning(this, "错误", QString("请求失败，状态码：%1").arg(statusCode));
        }
        });
}


void AdminWindow::onDeleteUser() {
    const QModelIndex index = ui->tableViewUsers->currentIndex();
    if (!index.isValid()) return;

    const int uid = userModel->item(index.row(), 0)->text().toInt();

    QUrl url(QString("http://127.0.0.1:8080/api/v1/user/%1").arg(uid));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->deleteResource(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        if (reply->error() == QNetworkReply::NoError) {
            // 后端删除成功，执行本地删除
            if (library->deleteUser(uid)) {
                refreshUserTable();
                QMessageBox::information(this, "删除用户", "用户删除成功！");
            }
            else {
                QMessageBox::warning(this, "删除用户", "后端删除成功，但本地删除失败！");
            }
        }
        else {
            QString errorMsg = reply->readAll();
            QMessageBox::warning(this, "删除用户失败", "错误: " + errorMsg);
        }
        reply->deleteLater();
        manager->deleteLater();
        });
}


void AdminWindow::onSearchUser() {
    const QString keyword = QInputDialog::getText(this, "搜索用户", "请输入用户 ID：");

    if (keyword.trimmed().isEmpty()) {
        QMessageBox::warning(this, "提示", "用户 ID 不能为空！");
        return;
    }

    bool ok;
    int userId = keyword.toInt(&ok);
    if (!ok || userId <= 0) {
        QMessageBox::warning(this, "错误", "请输入有效的正整数 ID！");
        return;
    }

    userModel->removeRows(0, userModel->rowCount()); // 清空表格

    // 构建请求
    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QUrl url(QString("http://localhost:8080/api/v1/user/%1").arg(userId));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkReply* reply = manager->get(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "请求错误", reply->errorString());
            return;
        }

        QVariant statusCodeVar = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute);
        int statusCode = statusCodeVar.isValid() ? statusCodeVar.toInt() : -1;

        if (statusCode == 404) {
            QMessageBox::information(this, "结果", "未找到该用户！");
            return;
        }
        else if (statusCode != 200) {
            QMessageBox::warning(this, "错误", QString("服务器返回状态码 %1").arg(statusCode));
            return;
        }

        QByteArray responseData = reply->readAll();
        QJsonParseError err;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(responseData, &err);

        if (err.error != QJsonParseError::NoError || !jsonDoc.isObject()) {
            QMessageBox::warning(this, "错误", "返回的用户数据解析失败: " + err.errorString());
            return;
        }

        QJsonObject userObj = jsonDoc.object();

        int id = userObj["id"].toInt();
        QString name = userObj["name"].toString();

        // 🔍 判断 group 是 string 还是 int
        QString group;
        if (userObj["group"].isString()) {
            group = userObj["group"].toString();
        }
        else if (userObj["group"].isDouble()) {
            int g = userObj["group"].toInt();
            group = (g == 1) ? "Admin" : "User";
        }
        else {
            group = "Unknown";
        }

        QJsonArray borrowedBooks = userObj["borrowedBooks"].toArray();

        userModel->appendRow({
            new QStandardItem(QString::number(id)),
            new QStandardItem(name),
            new QStandardItem(group),
            new QStandardItem(QString::number(borrowedBooks.size()))
            });
        });
}



void AdminWindow::onChangePasswordClicked() {
    const QModelIndex index = ui->tableViewUsers->currentIndex();
    if (!index.isValid()) {
        QMessageBox::warning(this, "提示", "请先选中一个用户！");
        return;
    }

    long long userId = userModel->item(index.row(), 0)->text().toLongLong();
    User* user = library->findUserById(userId);
    if (!user) {
        QMessageBox::warning(this, "错误", "未找到该用户！");
        return;
    }

    bool ok1, ok2;
    QString oldPassword = QInputDialog::getText(this, "修改密码", "请输入原密码：", QLineEdit::Password, "", &ok1);
    if (!ok1 || oldPassword.isEmpty()) return;

    QString newPassword = QInputDialog::getText(this, "修改密码", "请输入新密码：", QLineEdit::Password, "", &ok2);
    if (!ok2 || newPassword.isEmpty()) {
        QMessageBox::warning(this, "错误", "新密码不能为空！");
        return;
    }

    // 构造请求 JSON
    QJsonObject payload;
    payload["old_password"] = oldPassword;
    payload["new_password"] = newPassword;

    QUrl url(QString("http://localhost:8080/api/v1/user/%1/change-password").arg(userId));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->post(request, QJsonDocument(payload).toJson());

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "请求失败", "无法连接后端，请检查服务是否启动！");
            return;
        }

        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(reply->readAll(), &err);
        if (err.error != QJsonParseError::NoError) {
            QMessageBox::critical(this, "错误", "返回结果解析失败！");
            return;
        }

        QJsonObject res = doc.object();
        if (res.contains("message")) {
            user->setPassword(payload["new_password"].toString().toStdString()); // 更新前端用户密码
            trySaveData();
            QMessageBox::information(this, "成功", res["message"].toString());
        }
        else if (res.contains("error")) {
            QMessageBox::warning(this, "失败", res["error"].toString());
        }
        else {
            QMessageBox::information(this, "提示", "密码修改完成，但无明确返回信息。");
        }
        });
}

// 根据ID获取书籍信息
void AdminWindow::onGetBookById() {
    bool ok;
    int bookId = QInputDialog::getInt(this, "获取书籍", "请输入书籍ID：", 1, 1, 999999, 1, &ok);

    if (!ok) {
        return;
    }

    QUrl url(QString("http://127.0.0.1:8080/api/v1/book/%1").arg(bookId));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->get(request);

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "错误", "无法连接后端服务器！");
            return;
        }

        int statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();
        QByteArray response = reply->readAll();
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(response, &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            QMessageBox::critical(this, "错误", "返回数据格式有误！");
            return;
        }

        QJsonObject res = doc.object();

        if (statusCode == 200) {
            // 清空表格并显示单个书籍
            bookModel->removeRows(0, bookModel->rowCount());

            QList<QStandardItem*> row;
            row << new QStandardItem(res["title"].toString())
                << new QStandardItem(res["author"].toString())
                << new QStandardItem(res["publisher"].toString())
                << new QStandardItem(QString::number(res["publishYear"].toInt()))
                << new QStandardItem(res["ISBN"].toString())
                << new QStandardItem(QString::number(res["availableCopies"].toInt()))
                << new QStandardItem(QString::number(res["totalCopies"].toInt()));
            bookModel->appendRow(row);

            // 同步到本地Library
            Book book;
            book.fromJson(res);
            if (!library->findBookByISBN(book.getISBN())) {
                library->addBook(book);
                trySaveData();
            }

            QMessageBox::information(this, "成功", QString("找到书籍：%1").arg(res["title"].toString()));
        } else if (statusCode == 404) {
            QMessageBox::warning(this, "错误", "未找到该书籍！");
        } else {
            QString errorMsg = res.contains("error") ? res["error"].toString() : "未知错误";
            QMessageBox::warning(this, "错误", QString("获取失败：%1").arg(errorMsg));
        }
    });
}
