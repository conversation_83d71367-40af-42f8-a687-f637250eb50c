<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AdminWindow</class>
 <widget class="QMainWindow" name="AdminWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>600</height>
   </rect>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout">
    <item>
     <layout class="QHBoxLayout">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="welcomeLabel">
        <property name="text">
         <string>你好，管理员</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabBooks">
       <attribute name="title">
        <string>书籍管理</string>
       </attribute>
       <layout class="QVBoxLayout">
        <item>
         <widget class="QTableView" name="tableViewBooks"/>
        </item>
        <item>
         <layout class="QHBoxLayout">
          <item>
           <widget class="QComboBox" name="comboSearchBookType">
            <item>
             <property name="text">
              <string>书名</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>作者</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>出版社</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>ISBN</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnAddBook">
            <property name="text">
             <string>新增</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnEditBook">
            <property name="text">
             <string>编辑</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnDeleteBook">
            <property name="text">
             <string>删除</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnSearchBook">
            <property name="text">
             <string>搜索</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabUsers">
       <attribute name="title">
        <string>用户管理</string>
       </attribute>
       <layout class="QVBoxLayout">
        <item>
         <widget class="QTableView" name="tableViewUsers"/>
        </item>
        <item>
         <layout class="QHBoxLayout">
          <item>
           <widget class="QComboBox" name="comboSearchUserType">
            <item>
             <property name="text">
              <string>ID</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>姓名</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnAddUser">
            <property name="text">
             <string>新增</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnEditUser">
            <property name="text">
             <string>编辑</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnDeleteUser">
            <property name="text">
             <string>删除</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnSearchUser">
            <property name="text">
             <string>搜索</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnResetPassword">
            <property name="text">
             <string>重置密码</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="btnChangePassword">
            <property name="text">
             <string>修改密码</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="btnShowAllBooks">
      <property name="text">
       <string>返回主界面（书籍）</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="btnShowAllUsers">
      <property name="text">
       <string>返回主界面（用户）</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>18</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
