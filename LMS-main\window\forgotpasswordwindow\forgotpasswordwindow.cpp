//
// Created by <PERSON><PERSON><PERSON> on 25-6-30.
//
#include "forgotpasswordwindow.h"
#include <QMessageBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QRegularExpression>
#include <QIntValidator>

ForgotPasswordWindow::ForgotPasswordWindow(Library* library, QWidget* parent)
    : QDialog(parent), library(library), currentUser(nullptr), isVerified(false) {
    const QIcon icon(":/resources/favicon.png");
    this->setWindowIcon(icon);
    setWindowTitle("找回密码");
    setMinimumWidth(400);
    

    createWidgets();
    setupLayout();
    connectSignalsSlots();
}

ForgotPasswordWindow::~ForgotPasswordWindow() {}

void ForgotPasswordWindow::createWidgets() {
    idLabel = new QLabel("用户ID:");
    idEdit = new QLineEdit();
    idEdit->setPlaceholderText("请输入您的ID");
    idEdit->setValidator(new QIntValidator(1, 999999999, this));

    questionLabel = new QLabel("验证问题: 你妈妈的名字");

    answerEdit = new QLineEdit();
    answerEdit->setPlaceholderText("请输入答案");

    verifyButton = new QPushButton("验证");
    verifyButton->setMinimumWidth(100);

    passwordLabel = new QLabel("新密码:");
    passwordEdit = new QLineEdit();
    passwordEdit->setPlaceholderText("请输入新密码");
    passwordEdit->setEchoMode(QLineEdit::Password);

    confirmButton = new QPushButton("确定");
    confirmButton->setMinimumWidth(100);
    confirmButton->setEnabled(false);

    exitButton = new QPushButton("退出");
    exitButton->setMinimumWidth(100);
}

void ForgotPasswordWindow::setupLayout() {
    // 表单布局用于输入字段
    QFormLayout* formLayout = new QFormLayout();
    formLayout->addRow(idLabel, idEdit);
    formLayout->addRow(questionLabel, answerEdit);
    formLayout->addRow(passwordLabel, passwordEdit);

    // 按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(verifyButton);
    buttonLayout->addWidget(confirmButton);
    buttonLayout->addWidget(exitButton);

    // 主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->addLayout(formLayout);
    mainLayout->addLayout(buttonLayout);
    mainLayout->setSpacing(15);
    mainLayout->setContentsMargins(20,20,20,20);
}

void ForgotPasswordWindow::connectSignalsSlots() {
    connect(verifyButton, &QPushButton::clicked, this, &ForgotPasswordWindow::onVerifyButtonClicked);
    connect(confirmButton, &QPushButton::clicked, this, &ForgotPasswordWindow::onConfirmButtonClicked);
    connect(exitButton, &QPushButton::clicked, this, &ForgotPasswordWindow::onExitButtonClicked);
}

void ForgotPasswordWindow::onVerifyButtonClicked() {
    QString idStr = idEdit->text();
    QString answer = answerEdit->text();

    if (idStr.isEmpty() || answer.isEmpty()) {
        QMessageBox::warning(this, "错误", "请输入用户ID和答案");
        return;
    }

    bool ok;
    long long userId = idStr.toLongLong(&ok);
    if (!ok) {
        QMessageBox::warning(this, "错误", "用户ID格式不正确");
        return;
    }

    currentUser = library->findUserById(userId);
    if (!currentUser) {
        QMessageBox::warning(this, "错误", "未找到该用户");
        return;
    }

    // 简化的验证逻辑（实际应用中应从用户数据获取真实验证答案）
    QString storedAnswer = currentUser->getSecurityAnswer();
    if (answer == storedAnswer
        ) { // 实际应替换为用户存储的答案
        isVerified = true;
        confirmButton->setEnabled(true);
        QMessageBox::information(this, "验证成功", "验证通过，请设置新密码");
    } else {
        QMessageBox::warning(this, "验证失败", "答案不正确");
    }
}

void ForgotPasswordWindow::onConfirmButtonClicked() {
    if (!isVerified) {
        QMessageBox::warning(this, "提示", "请先通过验证");
        return;
    }

    QString newPassword = passwordEdit->text();
    if (newPassword.isEmpty()) {
        QMessageBox::warning(this, "错误", "新密码不能为空");
        return;
    }

    // 密码强度验证
    if (newPassword.length() < 6) {
        QMessageBox::warning(this, "错误", "密码长度至少为6位");
        return;
    }

    // 更新密码
    if (currentUser) {
        currentUser->setPassword(newPassword.toStdString());
        library->updateUser(*currentUser);
        library->saveToFile(library->getDataFilePaths()[0], library->getDataFilePaths()[1]);
        QMessageBox::information(this, "成功", "密码已成功重置");
        close();
    }
}

    void ForgotPasswordWindow::onExitButtonClicked() {
    close();
}

