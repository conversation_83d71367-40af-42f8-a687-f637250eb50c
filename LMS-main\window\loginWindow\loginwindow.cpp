#include "loginwindow.h"
#include "registerwindow.h" 
#include "forgotpasswordwindow.h"
#include <QLineEdit>
#include <QPushButton>
#include <QVBoxLayout>
#include <QIntValidator>
#include <QMessageBox>
#include <Library.h>

LoginWindow::LoginWindow(Library* library, QWidget* parent)
    : QWidget(parent), library(library) {
    const QIcon icon(":/resources/favicon.png");
    this->setWindowIcon(icon);

    idEdit = new QLineEdit(this);
    idEdit->setPlaceholderText("ID");
    idEdit->setValidator(new QIntValidator(1, 999999999, this));

    passwordEdit = new QLineEdit(this);
    passwordEdit->setPlaceholderText("密码");
    passwordEdit->setEchoMode(QLineEdit::Password);

    loginButton = new QPushButton("登录", this);
    registerButton = new QPushButton("注册", this);
    forgotPasswordButton = new QPushButton("忘记密码", this);

    auto* layout = new QVBoxLayout(this);
    layout->addWidget(idEdit);
    layout->addWidget(passwordEdit);
    layout->addWidget(loginButton);
    layout->addWidget(registerButton);
    layout->addWidget(forgotPasswordButton);

    connect(passwordEdit, &QLineEdit::returnPressed, this, &LoginWindow::onPasswordReturnPressed);
    connect(loginButton, &QPushButton::clicked, this, &LoginWindow::onLoginClicked);
    connect(registerButton, &QPushButton::clicked, this, &LoginWindow::onRegisterClicked);
    connect(forgotPasswordButton, &QPushButton::clicked, this, &LoginWindow::onForgotPasswordClicked);
}

void LoginWindow::onLoginClicked() {
    if (idEdit->text().isEmpty() || passwordEdit->text().isEmpty()) {
        QMessageBox::warning(this, "登录失败", "请输入用户ID和密码");
        return;
    }

    bool ok;
    const long id = idEdit->text().toLong(&ok);
    if (!ok) {
        QMessageBox::warning(this, "登录失败", "用户ID或密码错误");
        return;
    }

    const std::string password = passwordEdit->text().toStdString();

    if (User* user = library->loginUser(id, password)) {
        emit loginSuccessful(user);
    }
    else {
        QMessageBox::warning(this, "登录失败", "用户ID或密码错误");
    }
}

void LoginWindow::onPasswordReturnPressed() {
    onLoginClicked();
}

void LoginWindow::onRegisterClicked() {
    RegisterWindow* registerWindow = new RegisterWindow(library, this, this);
    registerWindow->show();
    this->hide();
}

void LoginWindow::onForgotPasswordClicked() {
    ForgotPasswordWindow forgotWindow(library, this);
    forgotWindow.setWindowModality(Qt::ApplicationModal);
    forgotWindow.exec();
}