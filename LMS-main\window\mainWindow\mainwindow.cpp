#include "mainwindow.h"
#include "ui_MainWindow.h"
#include <QMessageBox>
#include <QInputDialog>
#include <QHeaderView>

#include <QJsonObject>           // 构造 JSON
#include <QJsonDocument>         // JSON 转 QByteArray
#include <QNetworkRequest>       // 网络请求
#include <QNetworkReply>         // 网络响应
#include <QUrl>                  // URL

const QColor MainWindow::COLOR_OVERDUE(255, 150, 150);
const QColor MainWindow::COLOR_URGENT(255, 255, 100);

MainWindow::MainWindow(Library* library, User* user, QWidget* parent)
    : QMainWindow(parent), ui(new Ui::MainWindow), library(library), currentUser(user) {
    ui->setupUi(this);
    setWindowIcon(QIcon(":/resources/favicon.png"));

    // 设置欢迎语
    if (currentUser) {
        ui->welcomeLabel->setText(QString("你好，%1").arg(currentUser->getName()));
    }

    // 表格初始化
    ui->tableWidget->setColumnCount(8);
    ui->tableWidget->setHorizontalHeaderLabels(
        { "书名", "作者", "出版社", "出版年份", "ISBN", "可用数量", "是否已借阅","剩余时间"});
    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 添加搜索方式 ComboBox
    ui->searchTypeCombo->clear();
    ui->searchTypeCombo->addItems({ "书名", "作者", "ISBN" });

    // 连接信号槽
    connect(ui->searchButton, &QPushButton::clicked, this, &MainWindow::onSearchClicked);
    connect(ui->borrowButton, &QPushButton::clicked, this, &MainWindow::onBorrowClicked);
    connect(ui->returnButton, &QPushButton::clicked, this, &MainWindow::onReturnClicked);
    connect(ui->viewBorrowedButton, &QPushButton::clicked, this, &MainWindow::onViewBorrowedClicked);
    connect(ui->changePasswordButton, &QPushButton::clicked, this, &MainWindow::onChangePasswordClicked);
    connect(ui->returnMainButton, &QPushButton::clicked, this, &MainWindow::onReturnMainClicked);

    // 初始加载所有书籍
    populateTable(library->getAllBooks());
    checkDueBooks();
}

void MainWindow::onSearchClicked() const {
    QString keyword = ui->searchEdit->text().trimmed();
    QString type = ui->searchTypeCombo->currentText();
    std::vector<Book> result;

    if (keyword.isEmpty()) {
        result = library->getAllBooks();
    }
    else if (type == "书名") {
        result = library->searchBooksByTitle(keyword.toStdString());
    }
    else if (type == "作者") {
        result = library->searchBooksByAuthor(keyword.toStdString());
    }
    else if (type == "ISBN") {
        result = library->searchBooksByISBN(keyword.toStdString());
    }

    populateTable(result);
}

void MainWindow::populateTable(const std::vector<Book>& books) const {
    // 设置表格行数
    ui->tableWidget->setRowCount(static_cast<int>(books.size()));

    // 获取当前时间戳和30天期限（秒数）
    const std::time_t currentTime = std::time(nullptr);
    constexpr int LOAN_PERIOD_SECONDS = 30 * 24 * 3600;

    for (int i = 0; i < books.size(); ++i) {
        const Book& book = books[i];

        // 设置基础信息列（1-6列保持不变）
        ui->tableWidget->setItem(i, 0, new QTableWidgetItem(book.getTitle()));
        ui->tableWidget->setItem(i, 1, new QTableWidgetItem(book.getAuthor()));
        ui->tableWidget->setItem(i, 2, new QTableWidgetItem(book.getPublisher()));
        ui->tableWidget->setItem(i, 3, new QTableWidgetItem(QString::number(book.getPublishYear())));
        ui->tableWidget->setItem(i, 4, new QTableWidgetItem(book.getISBN()));
        ui->tableWidget->setItem(i, 5, new QTableWidgetItem(QString::number(book.getAvailableCopies())));

        // 第7列：是否已借阅
        const bool isBorrowed = library->isBookBorrowedByUser(
            currentUser->getId(),
            book.getISBN().toStdString()
        );
        ui->tableWidget->setItem(i, 6, new QTableWidgetItem(isBorrowed ? "是" : "否"));

        // 第8列：剩余时间（新增列）
        QTableWidgetItem* timeItem = new QTableWidgetItem();
        if (isBorrowed) {
            if (auto borrowTime = currentUser->getBorrowTime(book.getISBN())) {
                const std::time_t remainingSeconds = *borrowTime + LOAN_PERIOD_SECONDS - currentTime;

                // 设置显示文本和样式
                if (remainingSeconds <= 0) {
                    timeItem->setText("已过期");
                    timeItem->setBackground(QColor(255, 200, 200)); // 浅红背景
                    timeItem->setToolTip("该书已超过归还期限！");
                }
                else if (remainingSeconds <= 24 * 3600) { // 不足1天
                    timeItem->setText("不足1天！");
                    timeItem->setBackground(QColor(255, 255, 150)); // 浅黄背景
                    timeItem->setToolTip("请立即归还该书！");
                }
                else {
                    // 计算剩余天数和小时
                    const int days = remainingSeconds / (24 * 3600);
                    const int hours = (remainingSeconds % (24 * 3600)) / 3600;
                    timeItem->setText(QString("%1天%2小时").arg(days).arg(hours));

                    // 3天内添加温和提醒
                    if (days <= 3) {
                        timeItem->setBackground(QColor(255, 230, 200));
                        timeItem->setToolTip("该书即将到期，请及时归还");
                    }
                }
            }
            else {
                timeItem->setText("时间未知");
                timeItem->setBackground(Qt::lightGray);
            }
        }
        else {
            timeItem->setText(""); // 未借阅时留空
        }
        ui->tableWidget->setItem(i, 7, timeItem);
    }

    // 自动调整列宽（可选）
    ui->tableWidget->resizeColumnsToContents();
}

void MainWindow::onBorrowClicked() {
    int row = ui->tableWidget->currentRow();
    if (row < 0) {
        QMessageBox::warning(this, "提示", "请先选中一本书！");
        return;
    }
    QString isbn = ui->tableWidget->item(row, 4)->text();
    if (library->borrowBook(currentUser->getId(), isbn)) {
        QMessageBox::information(this, "成功", "借阅成功！");
        onSearchClicked();
    }
    else {
        QMessageBox::warning(this, "失败", "借阅失败，可能是已借阅或无库存。");
    }
}

void MainWindow::onReturnClicked() {
    const int row = ui->tableWidget->currentRow();
    if (row < 0) {
        QMessageBox::warning(this, "提示", "请先选中一本书！");
        return;
    }
    QString isbn = ui->tableWidget->item(row, 4)->text();
    if (library->returnBook(currentUser->getId(), isbn)) {
        QMessageBox::information(this, "成功", "归还成功！");
        onSearchClicked();
    }
    else {
        QMessageBox::warning(this, "失败", "归还失败，你未借阅这本书。");
    }
}

void MainWindow::onViewBorrowedClicked() const {
    auto borrowed = library->getBooksBorrowedByUser(currentUser->getId());
    populateTable(borrowed);
}

void MainWindow::onChangePasswordClicked() {
    bool ok1, ok2;
    QString oldPassword = QInputDialog::getText(this, "修改密码", "请输入原密码：", QLineEdit::Password, "", &ok1);
    if (!ok1 || oldPassword.isEmpty()) return;

    QString newPassword = QInputDialog::getText(this, "修改密码", "请输入新密码：", QLineEdit::Password, "", &ok2);
    if (!ok2 || newPassword.isEmpty()) {
        QMessageBox::warning(this, "错误", "新密码不能为空！");
        return;
    }

    QJsonObject payload;
    payload["old_password"] = oldPassword;
    payload["new_password"] = newPassword;

    long long userId = currentUser->getId();
    QUrl url(QString("http://localhost:8080/api/v1/user/%1/change-password").arg(userId));
    QNetworkRequest request(url);
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QNetworkAccessManager* manager = new QNetworkAccessManager(this);
    QNetworkReply* reply = manager->post(request, QJsonDocument(payload).toJson());

    connect(reply, &QNetworkReply::finished, this, [=]() {
        reply->deleteLater();
        manager->deleteLater();

        if (reply->error() != QNetworkReply::NoError) {
            QMessageBox::critical(this, "请求失败", "无法连接后端，请检查服务是否启动！");
            return;
        }

        QJsonParseError err;
        QJsonDocument doc = QJsonDocument::fromJson(reply->readAll(), &err);
        if (err.error != QJsonParseError::NoError) {
            QMessageBox::critical(this, "错误", "返回结果解析失败！");
            return;
        }

        QJsonObject res = doc.object();
        if (res.contains("message")) {
            currentUser->setPassword(newPassword.toStdString());
            trySaveData();
            QMessageBox::information(this, "成功", res["message"].toString());
        }
        else if (res.contains("error")) {
            QMessageBox::warning(this, "失败", res["error"].toString());
        }
        else {
            QMessageBox::information(this, "提示", "密码修改完成，但无明确返回信息。");
        }
        });
}


void MainWindow::trySaveData() {
    try {
        library->saveToFile(library->getDataFilePaths()[0], library->getDataFilePaths()[1]);
    }
    catch (const std::exception& e) {
        QMessageBox::critical(this, "保存失败", e.what());
    }
}

void MainWindow::checkDueBooks() {
    auto borrowedBooks = library->getBooksBorrowedByUser(currentUser->getId());
    QStringList dueBooks;

    for (const auto& book : borrowedBooks) {
        if (auto borrowTime = currentUser->getBorrowTime(book.getISBN())) {
            constexpr int LOAN_PERIOD = 30 * 24 * 3600;
            std::time_t remaining = *borrowTime + LOAN_PERIOD - std::time(nullptr);

            if (remaining > 0 && remaining <= 24 * 3600) {
                dueBooks << book.getTitle();
            }
        }
    }

    if (!dueBooks.isEmpty()) {
        QMessageBox::warning(this, "到期提醒",
            "以下书籍需在24小时内归还：\n" + dueBooks.join("\n"));
    }
}

void MainWindow::onReturnMainClicked() {
    // 重新显示所有图书
    populateTable(library->getAllBooks());
}

QString MainWindow::calculateRemainingTime(std::time_t borrowTime) const {
    constexpr int LOAN_PERIOD = 30 * 24 * 3600; // 30天的秒数
    std::time_t remaining = borrowTime + LOAN_PERIOD - std::time(nullptr);

    if (remaining <= 0) {
        return "已过期";
    }

    int days = remaining / (24 * 3600);
    int hours = (remaining % (24 * 3600)) / 3600;

    return QString("%1天%2小时").arg(days).arg(hours);
}