<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Library</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QHBoxLayout" name="topLayout">
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Policy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="welcomeLabel">
        <property name="text">
         <string>你好，用户</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="searchLayout">
      <item>
       <widget class="QLineEdit" name="searchEdit">
        <property name="placeholderText">
         <string>Enter keyword...</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QComboBox" name="searchTypeCombo">
        <item>
         <property name="text">
          <string>书名</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>作者</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>ISBN</string>
         </property>
        </item>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="searchButton">
        <property name="text">
         <string>Search</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QPushButton" name="changePasswordButton">
      <property name="text">
       <string>Change Password</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTableWidget" name="tableWidget">
      <property name="columnCount">
       <number>8</number>
      </property>
      <attribute name="horizontalHeaderDefaultSectionSize">
       <number>120</number>
      </attribute>
      <column>
       <property name="text">
        <string>书名</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>作者</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>出版社</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>出版年份</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>ISBN</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>可用数量</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>是否已借阅</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>剩余时间</string>
       </property>
      </column>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="borrowReturnLayout">
      <item>
       <widget class="QPushButton" name="borrowButton">
        <property name="text">
         <string>借阅</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="returnButton">
        <property name="text">
         <string>归还</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="viewBorrowedButton">
        <property name="text">
         <string>查看已借阅图书</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QPushButton" name="returnMainButton">
      <property name="text">
       <string>返回主界面</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>如遇到困难，请联系管理员  <EMAIL></string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1000</width>
     <height>25</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
