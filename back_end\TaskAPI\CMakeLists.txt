# CMake 最低版本要求 (用于现代 C++ 特性)
cmake_minimum_required(VERSION 3.10)

# 项目名称和语言
project(library-management-system LANGUAGES CXX)

# 设置 C++ 标准为 C++17
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# --- 查找必要的库 ---
# Windows 上的网络库 (TDM-GCC/MinGW 需要)
find_library(WS2_32_LIBRARY ws2_32)
find_library(WSOCK32_LIBRARY wsock32)
if(NOT WS2_32_LIBRARY OR NOT WSOCK32_LIBRARY)
    message(FATAL_ERROR "未找到 Winsock 库 (ws2_32, wsock32)。")
endif()

# --- 包含目录 ---
# 告诉 CMake 在哪里查找 Crow 和 nlohmann/json 的头文件
include_directories(include)

# --- 可执行文件 ---
# 定义可执行文件及其源文件
add_executable(library-management-system main.cpp)

# --- 链接库 ---
# 将可执行文件链接到所需的库
target_link_libraries(library-management-system PRIVATE
    ${WS2_32_LIBRARY}
    ${WSOCK32_LIBRARY}
    # 如果 Crow 需要其他库 (例如用于 HTTPS 的 ssl, crypto)，请在此处添加
)

# --- 可选：改善构建输出信息 ---
set(CMAKE_VERBOSE_MAKEFILE ON)

# --- 输出信息 ---
message(STATUS "已配置 library-management-system 项目。")
message(STATUS "包含路径: ${CMAKE_SOURCE_DIR}/include")
message(STATUS "源文件: ${CMAKE_SOURCE_DIR}/main.cpp")
message(STATUS "链接库: ${WS2_32_LIBRARY}; ${WSOCK32_LIBRARY}")