
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/tdm_gcc/TDM-GCC-64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/4.0.3/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/tdm_gcc/TDM-GCC-64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/4.0.3/CompilerIdCXX/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o"
      binary: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "D:/Dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-dynamic"
      Z_VCPKG_ROOT_DIR: "D:/Dev/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o'
        
        Run Build Command(s): D:/cmake/bin/cmake.exe -E env VERBOSE=1 D:\\tdm_gcc\\TDM-GCC-64\\bin\\mingw32-make.exe -f Makefile cmTC_4330d/fast
        D:/tdm_gcc/TDM-GCC-64/bin/mingw32-make  -f CMakeFiles\\cmTC_4330d.dir\\build.make CMakeFiles/cmTC_4330d.dir/build
        mingw32-make[1]: Entering directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o'
        Building C object CMakeFiles/cmTC_4330d.dir/CMakeCCompilerABI.c.obj
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj -c D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1.exe -quiet -v -iprefix D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cckI2nAl.s
        GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 68074fcaab9f6b1377b55f7cea05149b
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cckI2nAl.s
        GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1
        COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64'
        Linking C executable cmTC_4330d.exe
        D:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_4330d.dir\\link.txt --verbose=1
        D:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_4330d.dir/objects.a
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\ar.exe qc CMakeFiles\\cmTC_4330d.dir/objects.a @CMakeFiles\\cmTC_4330d.dir\\objects1.rsp
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a -Wl,--no-whole-archive -o cmTC_4330d.exe -Wl,--out-implib,libcmTC_4330d.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4330d.exe' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfJcmTE.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_4330d.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a --no-whole-archive --out-implib libcmTC_4330d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        collect2 version 10.3.0
        D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfJcmTE.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_4330d.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a --no-whole-archive --out-implib libcmTC_4330d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        GNU ld (GNU Binutils) 2.36.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4330d.exe' '-mtune=generic' '-march=x86-64'
        mingw32-make[1]: Leaving directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include] ==> [D:/tdm_gcc/TDM-GCC-64/include]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include;D:/tdm_gcc/TDM-GCC-64/include;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o']
        ignore line: []
        ignore line: [Run Build Command(s): D:/cmake/bin/cmake.exe -E env VERBOSE=1 D:\\tdm_gcc\\TDM-GCC-64\\bin\\mingw32-make.exe -f Makefile cmTC_4330d/fast]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/mingw32-make  -f CMakeFiles\\cmTC_4330d.dir\\build.make CMakeFiles/cmTC_4330d.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-3zjc0o']
        ignore line: [Building C object CMakeFiles/cmTC_4330d.dir/CMakeCCompilerABI.c.obj]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj -c D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1.exe -quiet -v -iprefix D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cckI2nAl.s]
        ignore line: [GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C17 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 68074fcaab9f6b1377b55f7cea05149b]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cckI2nAl.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1]
        ignore line: [COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4330d.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking C executable cmTC_4330d.exe]
        ignore line: [D:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_4330d.dir\\link.txt --verbose=1]
        ignore line: [D:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_4330d.dir/objects.a]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\ar.exe qc CMakeFiles\\cmTC_4330d.dir/objects.a @CMakeFiles\\cmTC_4330d.dir\\objects1.rsp]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a -Wl --no-whole-archive -o cmTC_4330d.exe -Wl --out-implib libcmTC_4330d.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4330d.exe' '-mtune=generic' '-march=x86-64']
        link line: [ D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfJcmTE.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_4330d.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a --no-whole-archive --out-implib libcmTC_4330d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfJcmTE.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [--exclude-libs=libpthread.a] ==> ignore
          arg [--undefined=__xl_f] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_4330d.exe] ==> ignore
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_4330d.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_4330d.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        ignore line: [collect2 version 10.3.0]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfJcmTE.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_4330d.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4330d.dir/objects.a --no-whole-archive --out-implib libcmTC_4330d.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        linker tool for 'C': D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> [D:/tdm_gcc/TDM-GCC-64/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> [D:/tdm_gcc/TDM-GCC-64/lib]
        implicit libs: [mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32]
        implicit objs: [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/crt2.o;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/default-manifest.o;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        implicit dirs: [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0;D:/tdm_gcc/TDM-GCC-64/lib/gcc;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib;D:/tdm_gcc/TDM-GCC-64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.36.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i"
      binary: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      VCPKG_INSTALLED_DIR: "D:/Dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-dynamic"
      Z_VCPKG_ROOT_DIR: "D:/Dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i'
        
        Run Build Command(s): D:/cmake/bin/cmake.exe -E env VERBOSE=1 D:\\tdm_gcc\\TDM-GCC-64\\bin\\mingw32-make.exe -f Makefile cmTC_95ca7/fast
        D:/tdm_gcc/TDM-GCC-64/bin/mingw32-make  -f CMakeFiles\\cmTC_95ca7.dir\\build.make CMakeFiles/cmTC_95ca7.dir/build
        mingw32-make[1]: Entering directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i'
        Building CXX object CMakeFiles/cmTC_95ca7.dir/CMakeCXXCompilerABI.cpp.obj
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj -c D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1plus.exe -quiet -v -iprefix D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmG2tyn.s
        GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"
        ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 10.3.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.23-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        Compiler executable checksum: 5e49fa89ed7bfe906173ae8e94e08de6
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmG2tyn.s
        GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1
        COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64'
        Linking CXX executable cmTC_95ca7.exe
        D:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_95ca7.dir\\link.txt --verbose=1
        D:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_95ca7.dir/objects.a
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\ar.exe qc CMakeFiles\\cmTC_95ca7.dir/objects.a @CMakeFiles\\cmTC_95ca7.dir\\objects1.rsp
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a -Wl,--no-whole-archive -o cmTC_95ca7.exe -Wl,--out-implib,libcmTC_95ca7.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 10.3.0 (tdm64-1) 
        COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/;D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95ca7.exe' '-mtune=generic' '-march=x86-64'
         D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccwxPZjF.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_95ca7.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a --no-whole-archive --out-implib libcmTC_95ca7.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        collect2 version 10.3.0
        D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccwxPZjF.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_95ca7.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a --no-whole-archive --out-implib libcmTC_95ca7.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o
        GNU ld (GNU Binutils) 2.36.1
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95ca7.exe' '-mtune=generic' '-march=x86-64'
        mingw32-make[1]: Leaving directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
          add: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include] ==> [D:/tdm_gcc/TDM-GCC-64/include]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        collapse include dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include;D:/tdm_gcc/TDM-GCC-64/include;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i']
        ignore line: []
        ignore line: [Run Build Command(s): D:/cmake/bin/cmake.exe -E env VERBOSE=1 D:\\tdm_gcc\\TDM-GCC-64\\bin\\mingw32-make.exe -f Makefile cmTC_95ca7/fast]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/mingw32-make  -f CMakeFiles\\cmTC_95ca7.dir\\build.make CMakeFiles/cmTC_95ca7.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-p25p6i']
        ignore line: [Building CXX object CMakeFiles/cmTC_95ca7.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj -c D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/cc1plus.exe -quiet -v -iprefix D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/ -D_REENTRANT D:\\cmake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mtune=generic -march=x86-64 -auxbase-strip CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmG2tyn.s]
        ignore line: [GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/tdm_gcc/TDM-GCC-64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../include]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed]
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [GNU C++14 (tdm64-1) version 10.3.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 10.3.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.23-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [Compiler executable checksum: 5e49fa89ed7bfe906173ae8e94e08de6]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [ D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccmG2tyn.s]
        ignore line: [GNU assembler version 2.36.1 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.36.1]
        ignore line: [COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95ca7.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=x86-64']
        ignore line: [Linking CXX executable cmTC_95ca7.exe]
        ignore line: [D:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_95ca7.dir\\link.txt --verbose=1]
        ignore line: [D:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_95ca7.dir/objects.a]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\ar.exe qc CMakeFiles\\cmTC_95ca7.dir/objects.a @CMakeFiles\\cmTC_95ca7.dir\\objects1.rsp]
        ignore line: [D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a -Wl --no-whole-archive -o cmTC_95ca7.exe -Wl --out-implib libcmTC_95ca7.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\tdm_gcc\\TDM-GCC-64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-git-10.3.0/configure --build=x86_64-w64-mingw32 --enable-targets=all --enable-languages=ada,c,c++,fortran,jit,lto,objc,obj-c++ --enable-libgomp --enable-lto --enable-graphite --enable-cxx-flags=-DWINPTHREAD_STATIC --disable-build-with-cxx --disable-build-poststage1-with-cxx --enable-libstdcxx-debug --enable-threads=posix --enable-version-specific-runtime-libs --enable-fully-dynamic-string --enable-libstdcxx-filesystem-ts=yes --disable-libstdcxx-pch --enable-libstdcxx-threads --enable-libstdcxx-time=yes --enable-mingw-wildcard --with-gnu-ld --disable-werror --enable-nls --disable-win32-registry --enable-large-address-aware --disable-rpath --disable-symvers --prefix=/mingw64tdm --with-local-prefix=/mingw64tdm --with-pkgversion=tdm64-1 --with-bugurl=https://github.com/jmeubank/tdm-gcc/issues]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 10.3.0 (tdm64-1) ]
        ignore line: [COMPILER_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95ca7.exe' '-mtune=generic' '-march=x86-64']
        link line: [ D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccwxPZjF.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_95ca7.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a --no-whole-archive --out-implib libcmTC_95ca7.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll] ==> ignore
          arg [-plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccwxPZjF.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [--exclude-libs=libpthread.a] ==> ignore
          arg [--undefined=__xl_f] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_95ca7.exe] ==> ignore
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_95ca7.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_95ca7.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-Bstatic] ==> search static
          arg [-lstdc++] ==> lib [SEARCH_STATIC:stdc++]
          arg [-Bdynamic] ==> search dynamic
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lpthread] ==> lib [pthread]
          arg [-lgcc] ==> lib [gcc]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmoldname] ==> lib [moldname]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o]
          arg [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        ignore line: [collect2 version 10.3.0]
        ignore line: [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/liblto_plugin-0.dll -plugin-opt=D:/tdm_gcc/TDM-GCC-64/bin/../libexec/gcc/x86_64-w64-mingw32/10.3.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccwxPZjF.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep --exclude-libs=libpthread.a --undefined=__xl_f -Bdynamic -o cmTC_95ca7.exe D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0 -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib -LD:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95ca7.dir/objects.a --no-whole-archive --out-implib libcmTC_95ca7.dll.a --major-image-version 0 --minor-image-version 0 -Bstatic -lstdc++ -Bdynamic -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lpthread -lgcc -lkernel32 -lmoldname -lmingwex -lmsvcrt -lkernel32 D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        linker tool for 'CXX': D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/bin/ld.exe
        search lib [SEARCH_STATIC:stdc++] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/libstdc++.a]
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib/default-manifest.o] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/default-manifest.o]
        collapse obj [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc] ==> [D:/tdm_gcc/TDM-GCC-64/lib/gcc]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../lib] ==> [D:/tdm_gcc/TDM-GCC-64/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/tdm_gcc/TDM-GCC-64/bin/../lib/gcc/x86_64-w64-mingw32/10.3.0/../../..] ==> [D:/tdm_gcc/TDM-GCC-64/lib]
        implicit libs: [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/libstdc++.a;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;pthread;gcc;kernel32;moldname;mingwex;kernel32]
        implicit objs: [D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/crt2.o;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtbegin.o;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib/default-manifest.o;D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/crtend.o]
        implicit dirs: [D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0;D:/tdm_gcc/TDM-GCC-64/lib/gcc;D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/lib;D:/tdm_gcc/TDM-GCC-64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.36.1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/cmake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "D:/cmake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/cmake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "D:/cmake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "D:/Dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow/Findasio.cmake:17 (find_package)"
      - "D:/Dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "D:/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow/CrowConfig.cmake:32 (find_dependency)"
      - "D:/Dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:14 (find_package)"
    directories:
      source: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-m43sjx"
      binary: "D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-m43sjx"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-static -static-libgcc -static-libstdc++ -lws2_32 -lwsock32"
      CMAKE_MODULE_PATH: "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow"
      VCPKG_INSTALLED_DIR: "D:/Dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-dynamic"
      Z_VCPKG_ROOT_DIR: "D:/Dev/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-m43sjx'
        
        Run Build Command(s): D:/cmake/bin/cmake.exe -E env VERBOSE=1 D:\\tdm_gcc\\TDM-GCC-64\\bin\\mingw32-make.exe -f Makefile cmTC_01763/fast
        D:/tdm_gcc/TDM-GCC-64/bin/mingw32-make  -f CMakeFiles\\cmTC_01763.dir\\build.make CMakeFiles/cmTC_01763.dir/build
        mingw32-make[1]: Entering directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-m43sjx'
        Building C object CMakeFiles/cmTC_01763.dir/src.c.obj
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles\\cmTC_01763.dir\\src.c.obj -c D:\\lib_copy\\LMS-main20250704\\back_end\\library-management-system-API\\CMakeFiles\\CMakeScratch\\TryCompile-m43sjx\\src.c
        Linking C executable cmTC_01763.exe
        D:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_01763.dir\\link.txt --verbose=1
        D:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_01763.dir/objects.a
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\ar.exe qc CMakeFiles\\cmTC_01763.dir/objects.a @CMakeFiles\\cmTC_01763.dir\\objects1.rsp
        D:\\tdm_gcc\\TDM-GCC-64\\bin\\gcc.exe -static -static-libgcc -static-libstdc++ -lws2_32 -lwsock32 -Wl,--whole-archive CMakeFiles\\cmTC_01763.dir/objects.a -Wl,--no-whole-archive -o cmTC_01763.exe -Wl,--out-implib,libcmTC_01763.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\\cmTC_01763.dir\\linkLibs.rsp
        mingw32-make[1]: Leaving directory 'D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/CMakeFiles/CMakeScratch/TryCompile-m43sjx'
        
      exitCode: 0
...
