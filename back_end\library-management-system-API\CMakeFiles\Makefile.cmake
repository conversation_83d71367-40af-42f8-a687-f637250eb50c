# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow/CrowConfig.cmake"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow/CrowTargets.cmake"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/crow/Findasio.cmake"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonConfig.cmake"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"
  "D:/Dev/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonTargets.cmake"
  "D:/Dev/vcpkg/scripts/buildsystems/vcpkg.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeCInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeDependentOption.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeRCInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CheckIncludeFile.cmake"
  "D:/cmake/share/cmake-4.0/Modules/CheckLibraryExists.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Compiler/GNU-C.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Compiler/GNU.cmake"
  "D:/cmake/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"
  "D:/cmake/share/cmake-4.0/Modules/FindPackageMessage.cmake"
  "D:/cmake/share/cmake-4.0/Modules/FindThreads.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Linker/GNU-C.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Linker/GNU-CXX.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Linker/GNU.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows-windres.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/Windows.cmake"
  "D:/cmake/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"
  "CMakeFiles/4.0.3/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeRCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "CMakeLists.txt"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/lms_server.dir/DependInfo.cmake"
  )
