# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\lib_copy\LMS-main20250704\back_end\library-management-system-API

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\lib_copy\LMS-main20250704\back_end\library-management-system-API

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/lms_server.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/lms_server.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/lms_server.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/lms_server.dir

# All Build rule for target.
CMakeFiles/lms_server.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\lms_server.dir\build.make CMakeFiles/lms_server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\lms_server.dir\build.make CMakeFiles/lms_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles --progress-num=1,2 "Built target lms_server"
.PHONY : CMakeFiles/lms_server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/lms_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/lms_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles 0
.PHONY : CMakeFiles/lms_server.dir/rule

# Convenience name for target.
lms_server: CMakeFiles/lms_server.dir/rule
.PHONY : lms_server

# codegen rule for target.
CMakeFiles/lms_server.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\lms_server.dir\build.make CMakeFiles/lms_server.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles --progress-num=1,2 "Finished codegen for target lms_server"
.PHONY : CMakeFiles/lms_server.dir/codegen

# clean rule for target.
CMakeFiles/lms_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\lms_server.dir\build.make CMakeFiles/lms_server.dir/clean
.PHONY : CMakeFiles/lms_server.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

