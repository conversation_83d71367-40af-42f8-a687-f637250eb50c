# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\lib_copy\LMS-main20250704\back_end\library-management-system-API

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\lib_copy\LMS-main20250704\back_end\library-management-system-API

# Include any dependencies generated for this target.
include CMakeFiles/lms_server.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/lms_server.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/lms_server.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/lms_server.dir/flags.make

CMakeFiles/lms_server.dir/codegen:
.PHONY : CMakeFiles/lms_server.dir/codegen

CMakeFiles/lms_server.dir/main.cpp.obj: CMakeFiles/lms_server.dir/flags.make
CMakeFiles/lms_server.dir/main.cpp.obj: CMakeFiles/lms_server.dir/includes_CXX.rsp
CMakeFiles/lms_server.dir/main.cpp.obj: main.cpp
CMakeFiles/lms_server.dir/main.cpp.obj: CMakeFiles/lms_server.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/lms_server.dir/main.cpp.obj"
	D:\tdm_gcc\TDM-GCC-64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/lms_server.dir/main.cpp.obj -MF CMakeFiles\lms_server.dir\main.cpp.obj.d -o CMakeFiles\lms_server.dir\main.cpp.obj -c D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\main.cpp

CMakeFiles/lms_server.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/lms_server.dir/main.cpp.i"
	D:\tdm_gcc\TDM-GCC-64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\main.cpp > CMakeFiles\lms_server.dir\main.cpp.i

CMakeFiles/lms_server.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/lms_server.dir/main.cpp.s"
	D:\tdm_gcc\TDM-GCC-64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\main.cpp -o CMakeFiles\lms_server.dir\main.cpp.s

# Object files for target lms_server
lms_server_OBJECTS = \
"CMakeFiles/lms_server.dir/main.cpp.obj"

# External object files for target lms_server
lms_server_EXTERNAL_OBJECTS =

lms_server.exe: CMakeFiles/lms_server.dir/main.cpp.obj
lms_server.exe: CMakeFiles/lms_server.dir/build.make
lms_server.exe: CMakeFiles/lms_server.dir/linkLibs.rsp
lms_server.exe: CMakeFiles/lms_server.dir/objects1.rsp
lms_server.exe: CMakeFiles/lms_server.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable lms_server.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\lms_server.dir\link.txt --verbose=$(VERBOSE)
	C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/Dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/lib_copy/LMS-main20250704/back_end/library-management-system-API/lms_server.exe -installedDir D:/Dev/vcpkg/installed/x64-mingw-dynamic/bin -OutVariable out

# Rule to build all files generated by this target.
CMakeFiles/lms_server.dir/build: lms_server.exe
.PHONY : CMakeFiles/lms_server.dir/build

CMakeFiles/lms_server.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\lms_server.dir\cmake_clean.cmake
.PHONY : CMakeFiles/lms_server.dir/clean

CMakeFiles/lms_server.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\lib_copy\LMS-main20250704\back_end\library-management-system-API D:\lib_copy\LMS-main20250704\back_end\library-management-system-API D:\lib_copy\LMS-main20250704\back_end\library-management-system-API D:\lib_copy\LMS-main20250704\back_end\library-management-system-API D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\CMakeFiles\lms_server.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/lms_server.dir/depend

