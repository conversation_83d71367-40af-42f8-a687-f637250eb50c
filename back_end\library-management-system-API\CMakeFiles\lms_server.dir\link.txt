D:\cmake\bin\cmake.exe -E rm -f CMakeFiles\lms_server.dir/objects.a
D:\tdm_gcc\TDM-GCC-64\bin\ar.exe qc CMakeFiles\lms_server.dir/objects.a @CMakeFiles\lms_server.dir\objects1.rsp
D:\tdm_gcc\TDM-GCC-64\bin\g++.exe -static -static-libgcc -static-libstdc++ -lws2_32 -lwsock32 -Wl,--whole-archive CMakeFiles\lms_server.dir/objects.a -Wl,--no-whole-archive -o lms_server.exe -Wl,--out-implib,liblms_server.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\lms_server.dir\linkLibs.rsp
