CMakeFiles/lms_server.dir/main.cpp.obj: \
 D:\lib_copy\LMS-main20250704\back_end\library-management-system-API\main.cpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/query_string.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stdio.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/corecrt_stdio_config.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/corecrt.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_mac.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_secapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/vadefs.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_off_t.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/swprintf.inl \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/stdio_s.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/string.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/crtdefs.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/string_s.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++config.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/pstl_config.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stringfwd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/memoryfwd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/char_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algobase.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functexcept.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_defines.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cpp_type_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/type_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/numeric_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_pair.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/move.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/type_traits \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_types.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator_base_funcs.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/concept_check.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/assertions.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_iterator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ptr_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/debug/debug.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/predefined_ops.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/postypes.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwchar \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wchar.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/corecrt_wstdlib.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_stat64.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/wchar_s.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdint \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdint.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stdint.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stddef.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stddef.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/new_allocator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/new \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/exception \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/exception_ptr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_init_exception.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/typeinfo \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hash_bytes.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/nested_exception.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/localefwd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/clocale \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/locale.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iosfwd \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cctype \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ctype.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream_insert.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/cxxabi_forced.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_function.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/binders.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_access.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/initializer_list \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/iterator_concepts.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/concepts \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/range_cmp.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/atomicity.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pthread.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/errno.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sys/types.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/process.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/corecrt_startup.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/limits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include-fixed/syslimits.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/limits.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/signal.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pthread_signal.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sys/timeb.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pthread_compat.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pthread_unistd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/alloc_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/alloc_traits.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_construct.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/string_view \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/functional_hash.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/string_view.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/string_conversions.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdlib \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stdlib.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdlib.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/malloc.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_abs.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstdio \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cerrno \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/charconv.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_string.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/vector \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_uninitialized.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_vector.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_bvector.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/vector.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/unordered_map \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/aligned_buffer.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/hashtable_policy.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tuple \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/utility \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_relops.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/array \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uses_allocator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/invoke.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/limits \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/node_handle.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/optional \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/enable_special_members.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unordered_map.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/erase_if.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iostream \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ostream \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ios \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ios_base.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_classes.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/system_error \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/stdexcept \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/streambuf \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cwctype \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wctype.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/streambuf_iterator.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/basic_ios.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ostream.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/istream \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/istream.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/memory \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tempbuf.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_raw_storage_iter.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/ranges_uninitialized.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ext/concurrence.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_ptr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_base.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/allocated_ptr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/refwrap.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/shared_ptr_atomic.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_base.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_lockfree_defines.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/backward/auto_ptr.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_memory_defs.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/execution_defs.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/http_parser_merged.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/assert.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/common.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/utility.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstring \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/functional \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_function.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_algo.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/algorithmfwd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_heap.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/uniform_int_dist.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/sstream \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/sstream.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/random \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cmath \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/math.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/specfun.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/gamma.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/special_function_util.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/bessel_function.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/beta_function.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/ell_integral.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/exp_integral.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/hypergeometric.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/legendre_function.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/modified_bessel_func.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_hermite.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/poly_laguerre.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/tr1/riemann_zeta.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/random.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/opt_random.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/random.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/numeric \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_numeric.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_numeric_defs.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/algorithm \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/pstl/glue_algorithm_defs.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/settings.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/filesystem \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_fwd.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/chrono \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ratio \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/ctime \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/time.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_timeval.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pthread_time.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/parse_numbers.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_path.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/locale \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/time_members.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/codecvt.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_facets_nonio.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/locale_conv.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iomanip \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/quoted_string.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/codecvt \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_dir.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fs_ops.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/ci_map.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/TinySHA1.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/socket_adaptors.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/any_completion_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/config.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/version \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/type_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/invocable_archetype.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/push_options.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/pop_options.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/equality_comparable.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/execute_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_applicable_property.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/query_static_constexpr_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/static_query.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/any_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/assert.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cassert \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/atomic_count.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/atomic \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/cstddef.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cstddef \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/executor_function.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/handler_alloc_helpers.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/memory.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/cstdint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/throw_exception.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/recycling_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/thread_context.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/climits \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/call_stack.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/noncopyable.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/tss_ptr.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_tss_ptr.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/socket_types.hpp \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winsock2.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_unicode.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/windows.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sdkddkver.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/excpt.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/stdarg.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stdarg.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_mingw_stdarg.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/windef.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winapifamily.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/minwindef.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/specstrings.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sal.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/concurrencysal.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/driverspecs.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winnt.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/apiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/intrin-impl.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/basetsd.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/guiddef.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/x86intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/ia32intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/immintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/mmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xmmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/mm_malloc.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/emmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/pmmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/tmmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/smmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/popcntintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/wmmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/fxsrintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xsaveintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xsaveoptintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xsavesintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xsavecintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avxintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx2intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512fintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512erintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512pfintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512cdintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512bwintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512dqintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vlbwintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vldqintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512ifmaintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512ifmavlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vbmiintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vbmivlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx5124fmapsintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx5124vnniwintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vpopcntdqintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vbmi2intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vbmi2vlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vnniintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vnnivlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vpopcntdqvlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512bitalgintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vp2intersectintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512vp2intersectvlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/shaintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/lzcntintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/bmiintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/bmi2intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/fmaintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/f16cintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/rtmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xtestintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/cetintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/gfniintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/vaesintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/vpclmulqdqintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/movdirintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/sgxintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/pconfigintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/waitpkgintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/cldemoteintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512bf16vlintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/avx512bf16intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/enqcmdintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/rdseedintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/prfchwintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/adxintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/clwbintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/clflushoptintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/wbnoinvdintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/pkuintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/mm3dnow.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/fma4intrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/ammintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/xopintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/lwpintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/tbmintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/mwaitxintrin.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/clzerointrin.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack4.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/poppack.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack4.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack2.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/poppack.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack2.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack8.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack8.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ktmtypes.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winbase.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/apisetcconv.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/minwinbase.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/bemapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/debugapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/errhandlingapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/fibersapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/fileapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/handleapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/heapapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ioapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/interlockedapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/jobapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/libloaderapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/memoryapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/namedpipeapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/namespaceapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/processenv.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/processthreadsapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/processtopologyapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/profileapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/realtimeapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/securityappcontainer.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/securitybaseapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/synchapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sysinfoapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/systemtopologyapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/threadpoolapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/threadpoollegacyapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/utilapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wow64apiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winerror.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/fltwinerror.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/timezoneapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wingdi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/pshpack1.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winuser.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/tvout.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winnls.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/datetimeapi.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stringapiset.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wincon.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winver.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winreg.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/reason.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winnetwk.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/wnnc.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/virtdisk.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/stralign.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sec_api/stralign_s.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/winsvc.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/mcx.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/imm.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_ws1_undef.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/_bsd_types.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/inaddr.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_socket_types.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_fd_types.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_ip_types.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_wsadata.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ws2def.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_wsa_errnos.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/qos.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ws2tcpip.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/ws2ipdef.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/in6addr.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_ip_mreq1.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/mstcpip.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/mswsock.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/psdk_inc/_xmitfile.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/old_win_sdk_compat.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_tss_ptr.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/throw_error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/error_code.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/error_code.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/local_free_on_block_exit.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/throw_error.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/error.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/thread_context.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/thread_info_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/multiple_exceptions.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/multiple_exceptions.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/functional.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/non_const_lvalue.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scoped_ptr.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/bad_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/impl/bad_executor.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/blocking.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/prefer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/prefer_free.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/prefer_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/require_free.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/require_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/static_require.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/query.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/query_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/query_free.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/require.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/blocking_adaptation.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/event.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_event.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_event.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/mutex.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_mutex.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scoped_lock.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_mutex.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/context.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/any \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/context_as.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/mapping.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/occupancy.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/outstanding_work.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/prefer_only.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/relationship.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/any_completion_executor.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/any_completion_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/any_completion_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/any_io_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/execution_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/handler_type_requirements.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/async_result.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/default_completion_token.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/deferred.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/utility.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/deferred.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/service_registry.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/service_registry.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/service_registry.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/execution_context.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/any_io_executor.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_cancellation_slot.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_signal.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_type.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/cancellation_signal.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/is_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/system_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/executor_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/fenced_block.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/std_fenced_block.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scheduler_operation.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/handler_tracking.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/handler_tracking.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/op_queue.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/global.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_global.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/static_mutex.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_static_mutex.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_static_mutex.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scheduler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/conditionally_enabled_event.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/conditionally_enabled_mutex.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/null_event.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/null_event.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scheduler_task.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/thread.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/posix_thread.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/posix_thread.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/scheduler.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/concurrency_hint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/limits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/scheduler_thread_info.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/signal_blocker.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/null_signal_blocker.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/reactor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/null_reactor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/thread_group.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/system_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/system_context.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_immediate_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_state.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/recycling_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/any_io_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/append.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/append.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/handler_cont_helpers.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/handler_continuation_hook.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/initiation_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/as_tuple.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/as_tuple.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_cancellation_slot.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associated_immediate_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/associator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/async_result.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/awaitable.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_datagram_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/io_object_impl.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/chrono.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/wrapped_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/bind_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/winsock_init.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/winsock_init.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_io_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_queue_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/operation.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_operation.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_queue_set.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/timer_queue_set.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/wait_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_thread_info.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_io_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/completion_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/handler_work.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/initiate_dispatch.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/work_dispatcher.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/executor_work_guard.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_io_context.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/io_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/io_context.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/post.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/initiate_post.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/socket_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/io_control.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/socket_option.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/buffer_sequence_adapter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/array_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/string_view.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_contiguous_iterator.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/iterator \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stream_iterator.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/is_buffer_sequence.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/registered_buffer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/buffer_sequence_adapter.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/reactor_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/select_reactor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/fd_set_adapter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/posix_fd_set_adapter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_fd_set_adapter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/reactor_op_queue.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/hash_map.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/list \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_list.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/list.tcc \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/select_interrupter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/socket_select_interrupter.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/socket_select_interrupter.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/socket_holder.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/socket_ops.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/socket_ops.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/select_reactor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/select_reactor.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_null_buffers_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_accept_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_service_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_connect_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_send_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_recv_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_recvmsg_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_wait_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_socket_service_base.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_socket_recvfrom_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_deadline_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/file_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_file_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_handle_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_handle_read_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_handle_write_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_handle_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_file_service.ipp \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/sys/stat.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/io.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_io_object.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_random_access_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_raw_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_readable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_seq_packet_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_serial_port.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/serial_port_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/serial_port_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/serial_port_base.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_serial_port_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_iocp_serial_port_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_signal_set.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/signal_set_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/signal_set_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/signal_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/signal_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/signal_set_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_acceptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_iostream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_streambuf.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_stream_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/steady_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_waitable_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/chrono_time_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/deadline_timer_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_queue.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/date_time_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_queue_ptime.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_scheduler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timer_scheduler_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/wait_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/wait_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_streambuf.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_stream_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_stream_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_streambuf.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_streambuf_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_waitable_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_writable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/bind_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/bind_cancellation_slot.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/bind_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/uses_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/bind_immediate_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffer_registration.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_read_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_read_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_read_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/buffer_resize_guard.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/buffered_stream_storage.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/buffered_read_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_read_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_write_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_write_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/completion_condition.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/write.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/write.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/base_from_cancellation_state.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/base_from_completion_cond.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/consuming_buffers.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/dependent_type.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/buffered_write_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_write_stream_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffered_write_stream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffers_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancel_after.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/cancel_after.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/timed_cancel_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/completion_payload.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/completion_message.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/variant \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/completion_payload_handler.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancel_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/cancel_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_signal.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_state.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/cancellation_type.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/co_composed.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/co_spawn.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/completion_condition.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/compose.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/composed.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/composed_work.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/composed.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/connect.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/connect.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/connect_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_readable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_writable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/connect_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/connect_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/connect_pipe.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/consign.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/consign.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/coroutine.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/deadline_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/defer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/initiate_defer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/deferred.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/default_completion_token.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detached.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/detached.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/dispatch.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/error_code.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/any_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/blocking.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/blocking_adaptation.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/context_as.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/invocable_archetype.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/mapping.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/occupancy.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/outstanding_work.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/prefer_only.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/execution/relationship.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/executor.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/executor_work_guard.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/file_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/detail/endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/detail/impl/endpoint.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/datagram_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_datagram_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/raw_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_raw_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/seq_packet_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_seq_packet_socket.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/generic/stream_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_iostream.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/handler_continuation_hook.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/high_resolution_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/immediate.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/dispatch.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_context_strand.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/strand_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/strand_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/strand_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_service_strand.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/io_context_strand.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/array.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address_v4.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address_v6.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/bad_address_cast.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/address.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4_range.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6_range.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/network_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v4_range.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/network_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/network_v4.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/network_v4.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/network_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/address_v6_range.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/network_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/network_v6.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/network_v6.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/bad_address_cast.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/detail/endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/detail/impl/endpoint.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_entry.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_query.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/resolver_query_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/resolver_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_results.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/resolver_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/resolve_endpoint_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/resolve_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/resolve_query_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/resolver_service_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/resolver_service_base.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_entry.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver_query.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/host_name.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/impl/host_name.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/host_name.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/icmp.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/basic_resolver.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/multicast.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/detail/socket_option.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/resolver_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/resolver_query_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/tcp.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_socket_acceptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/udp.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/unicast.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/ip/v6_only.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_applicable_property.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_contiguous_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_read_buffered.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/is_write_buffered.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/detail/endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/detail/impl/endpoint.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/connect_pair.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/basic_endpoint.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/datagram_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/seq_packet_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/local/stream_protocol.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/multiple_exceptions.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/packaged_task.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/future.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/future \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/mutex \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/std_mutex.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/unique_lock.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/thread \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/condition_variable \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/atomic_futex.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/placeholders.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/basic_descriptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/basic_stream_descriptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/basic_descriptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/descriptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/descriptor_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/posix/stream_descriptor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/post.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/prefer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/prepend.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/prepend.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/query.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/random_access_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_random_access_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/read.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/read.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/read_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/read_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/read_until.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/regex_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/read_until.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/buffers_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/readable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/recycling_allocator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/redirect_error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/redirect_error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/registered_buffer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/require.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/require_concept.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/require_concept_member.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/require_concept_free.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/traits/static_require_concept.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/serial_port.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_serial_port.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/serial_port_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/signal_set.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_signal_set.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/signal_set_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/socket_base.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/static_thread_pool.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/thread_pool.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/thread_pool.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/blocking_executor_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/thread_pool.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/steady_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/strand.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/strand_executor_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/strand_executor_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/defer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/strand_executor_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/stream_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_stream_file.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/streambuf.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/basic_streambuf.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_context.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_error.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/system_timer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/this_coro.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/thread.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/thread_pool.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/time_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/use_awaitable.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/use_future.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/use_future.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/packaged_task.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/uses_executor.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/version.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/wait_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_object_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_object_handle_service.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/impl/win_object_handle_service.ipp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_overlapped_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_random_access_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_overlapped_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_stream_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/object_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_object_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/overlapped_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/overlapped_ptr.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_overlapped_ptr.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/detail/win_iocp_overlapped_op.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/random_access_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_random_access_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/stream_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/windows/basic_stream_handle.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/writable_pipe.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/write.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/write_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/impl/write_at.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/asio/version.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/json.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/cfloat \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/float.h \
 D:/tdm_gcc/TDM-GCC-64/x86_64-w64-mingw32/include/float.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/returnable.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/logging.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/mustache.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/fstream \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/basic_file.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/x86_64-w64-mingw32/bits/c++io.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/fstream.tcc \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/task_timer.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/map \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_tree.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_map.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/stl_multimap.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/http_request.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/websocket.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/TinySHA1.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/parser.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/http_response.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/mime_types.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/multipart.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/exceptions.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/multipart_view.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/charconv \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bit \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/routing.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/middleware.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/middleware_context.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/compression.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/http_connection.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/compression.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/http_server.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/version.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/crow/app.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/json.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/adl_serializer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/abi_macros.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/conversions/from_json.hpp \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/forward_list \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/forward_list.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/forward_list.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/valarray \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/valarray_array.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/valarray_array.tcc \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/valarray_before.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/slice_array.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/valarray_after.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/gslice.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/gslice_array.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/mask_array.h \
 D:/tdm_gcc/TDM-GCC-64/lib/gcc/x86_64-w64-mingw32/10.3.0/include/c++/bits/indirect_array.h \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/exceptions.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/value_t.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/macro_scope.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/detected.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/void_t.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/thirdparty/hedley/hedley.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/string_escape.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/position_t.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/cpp_future.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/type_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/iterator_traits.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/call_std/begin.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/call_std/end.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/json_fwd.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/string_concat.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/identity_tag.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/std_fs.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/conversions/to_json.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/iteration_proxy.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/string_utils.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/byte_container_with_subtype.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/hash.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/binary_reader.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/input_adapters.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/json_sax.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/lexer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/meta/is_sax.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/input/parser.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/internal_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/primitive_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/iter_impl.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/iterators/json_reverse_iterator.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/json_custom_base_class.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/json_pointer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/json_ref.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/output/binary_writer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/output/output_adapters.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/output/serializer.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/conversions/to_chars.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/ordered_map.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/detail/macro_unscope.hpp \
 D:/Dev/vcpkg/installed/x64-mingw-dynamic/include/nlohmann/thirdparty/hedley/hedley_undef.hpp
