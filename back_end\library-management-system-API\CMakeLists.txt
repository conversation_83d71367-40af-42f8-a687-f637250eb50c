cmake_minimum_required(VERSION 3.15)
project(library-management-system-API)

# 设置 vcpkg 工具链文件
set(CMAKE_TOOLCHAIN_FILE "D:/course/DevOps/library-management-system/back_end/library-management-system-API/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file")

# MinGW 静态链接设置
set(CMAKE_CXX_STANDARD 17)
if(MINGW)
    set(CMAKE_EXE_LINKER_FLAGS "-static -static-libgcc -static-libstdc++ -lws2_32 -lwsock32")
endif()

# 查找依赖
find_package(Crow REQUIRED)
find_package(nlohmann_json REQUIRED)

# 创建可执行文件
add_executable(lms_server main.cpp)

# 链接库
target_link_libraries(lms_server PRIVATE
    Crow::Crow
    nlohmann_json::nlohmann_json
    ws2_32
    wsock32
)