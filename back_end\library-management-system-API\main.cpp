#define CROW_MAIN // 在包含 crow.h 之前定义此宏，以便生成 main() 函数
#include "crow.h" // 如果你下载的是 crow_all.h，就用那个文件名
#include "nlohmann/json.hpp"

// 标准 C++ 头文件
#include <string>    // 字符串处理
#include <vector>    // 向量 (虽然这里主要用 json 数组)
#include <mutex>     // 互斥锁，用于线程同步
#include <atomic>    // 原子操作，用于线程安全的 ID 生成
#include <chrono>    // 时间处理
#include <iomanip>   // I/O 格式化 (例如 put_time)
#include <sstream>   // 字符串流
#include <iostream>  // 用于输出日志/错误信息
#include <memory>    // 用于智能指针 (管理 FILE*)
#include <algorithm> // 用于 std::find_if, std::max
#include <random>    // 用于生成随机密码

// C 标准头文件 (用于二进制文件 I/O)
#include <cstdio> // 包含 C 标准 I/O 头文件 (fopen, fread, fwrite, fclose 等)

// 使用 nlohmann::json 简化 JSON 操作
using json = nlohmann::json;

// --- 配置 ---
const std::string USER_FILE = "user.json"; // 用户数据文件名
const std::string BOOK_FILE = "book.json"; // 书籍数据文件名

// --- 全局状态与同步 ---
// 使用互斥锁来保护文件访问和内存中的数据
std::mutex user_mutex;
std::mutex book_mutex;
// 使用原子整数来确保线程安全地生成下一个 ID
std::atomic<int> next_user_id (1); // 用户ID从1开始
std::atomic<int> next_book_id (1); // 书籍ID从1开始

// --- 辅助函数 ---

// 获取当前时间的 ISO 8601 格式字符串 (UTC)
std::string getCurrentTimestampISO8601()
{
    auto now = std::chrono::system_clock::now();
    auto itt = std::chrono::system_clock::to_time_t(now);
    std::ostringstream ss;
    // 使用 std::gmtime 获取 UTC 时间
    ss << std::put_time(std::gmtime(&itt), "%Y-%m-%dT%H:%M:%SZ");
    return ss.str();
}

// 生成随机密码
std::string generateRandomPassword(int length = 8)
{
    const std::string charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, charset.size() - 1);

    std::string password;
    for (int i = 0; i < length; ++i)
    {
        password += charset[dis(gen)];
    }
    return password;
}

// 从 JSON 文件加载数据 (兼容对象结构与数组结构)
json loadData(const std::string &filename)
{
    json data = json::array(); // 默认返回空用户数组

    std::unique_ptr<FILE, decltype(&fclose)> file_ptr(
        fopen(filename.c_str(), "rb"),
        fclose);
    FILE *file = file_ptr.get();

    if (file)
    {
        fseek(file, 0, SEEK_END);
        long file_size = ftell(file);
        rewind(file);

        if (file_size > 0)
        {
            std::string buffer(file_size, '\0');
            size_t bytes_read = fread(&buffer[0], 1, file_size, file);

            if (bytes_read == static_cast<size_t>(file_size))
            {
                try
                {
                    json parsed = json::parse(buffer);

                    if (parsed.is_array())
                    {
                        data = parsed;
                    }
                    else if (parsed.is_object() && parsed.contains("users") && parsed["users"].is_array())
                    {
                        data = parsed["users"];
                    }
                    else
                    {
                        std::cerr << "警告: " << filename << " 内容解析后不包含有效用户数组，将使用空数组。" << std::endl;
                    }
                }
                catch (json::parse_error &e)
                {
                    std::cerr << "错误: 解析 " << filename << " 失败: " << e.what() << std::endl;
                }
            }
            else
            {
                std::cerr << "错误: 读取 " << filename << " 时发生错误 (预期读取 " << file_size << " 字节, 实际读取 " << bytes_read << " 字节)。" << std::endl;
            }
        }
    }

    return data;
}


// 将数据保存到 JSON 文件 (使用二进制 I/O，支持 UTF-8)
bool saveData(const json &data, const std::string &filename)
{
    // 确保我们正在保存的是一个 JSON 数组
    if (!data.is_array())
    {
        std::cerr << "错误: 尝试将非数组类型的 JSON 数据保存到 " << filename << std::endl;
        return false;
    }

    try
    {
        // 将 JSON 对象序列化为带缩进的 UTF-8 字符串
        std::string buffer = data.dump(2); // 2 个空格缩进

        // 使用 C 风格 fopen 以二进制写入模式 ("wb") 打开文件
        std::unique_ptr<FILE, decltype(&fclose)> file_ptr(
            fopen(filename.c_str(), "wb"), // "wb" = write binary (二进制写)
            fclose);
        FILE *file = file_ptr.get();

        if (file)
        { // 文件成功打开
            // 将序列化后的字符串内容写入文件
            size_t bytes_written = fwrite(buffer.c_str(), 1, buffer.length(), file);

            if (bytes_written == buffer.length())
            { // 确保所有字节都已写入
                return true;
            }
            else
            {
                std::cerr << "错误: 写入 " << filename << " 时发生错误 (预期写入 " << buffer.length() << " 字节, 实际写入 " << bytes_written << " 字节)。" << std::endl;
                return false;
            }
        }
        else
        { // 文件打开失败
            std::cerr << "错误: 无法打开 " << filename << " 进行写入。" << std::endl;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "错误: JSON 序列化过程中发生异常: " << e.what() << std::endl;
        return false;
    }
}

// 根据现有数据初始化下一个ID
void initializeNextUserId(const json &users_data)
{
    int max_id = 0;
    if (users_data.is_array())
    {
        for (const auto &user : users_data)
        {
            if (user.contains("id") && user["id"].is_number_integer())
            {
                max_id = std::max(max_id, user["id"].get<int>());
            }
        }
    }
    next_user_id = max_id + 1;
    std::cout << "信息: 初始化下一个用户 ID 为: " << next_user_id << std::endl;
}

void initializeNextBookId(const json &books_data)
{
    int max_id = 0;
    if (books_data.is_array())
    {
        for (const auto &book : books_data)
        {
            if (book.contains("id") && book["id"].is_number_integer())
            {
                max_id = std::max(max_id, book["id"].get<int>());
            }
        }
    }
    next_book_id = max_id + 1;
    std::cout << "信息: 初始化下一个书籍 ID 为: " << next_book_id << std::endl;
}

// 验证用户输入数据
bool validateUserInput(const json &user_input, bool is_update = false)
{
    // 验证必需字段 'name'
    if (!user_input.contains("name") || !user_input["name"].is_string() || user_input["name"].get<std::string>().empty())
    {
        return false;
    }

    // 对于新建用户，密码是必需的；对于更新，密码是可选的
    if (!is_update)
    {
        if (!user_input.contains("password") || !user_input["password"].is_string() || user_input["password"].get<std::string>().empty())
        {
            return false;
        }
    }
    else
    {
        // 更新时，如果提供了密码，必须是非空字符串
        if (user_input.contains("password") && (!user_input["password"].is_string() || user_input["password"].get<std::string>().empty()))
        {
            return false;
        }
    }

    // 验证可选字段 'group' (如果存在)
    if (user_input.contains("group"))
    {
        if (!user_input["group"].is_string())
        {
            return false;
        }
        std::string group = user_input["group"];
        if (group != "User" && group != "Admin")
        {
            return false;
        }
    }

    return true;
}

// 验证书籍输入数据
bool validateBookInput(const json &book_input)
{
    // 验证必需字段
    if (!book_input.contains("ISBN") || !book_input["ISBN"].is_string() || book_input["ISBN"].get<std::string>().empty())
    {
        return false;
    }
    if (!book_input.contains("title") || !book_input["title"].is_string() || book_input["title"].get<std::string>().empty())
    {
        return false;
    }
    if (!book_input.contains("totalCopies") || !book_input["totalCopies"].is_number_integer() || book_input["totalCopies"].get<int>() < 0)
    {
        return false;
    }

    return true;
}

// 查找用户通过ID
json *findUserById(json &users, int userId)
{
    auto it = std::find_if(users.begin(), users.end(),
                           [userId](const json &user)
                           {
                               return user.contains("id") && user["id"].is_number_integer() && user["id"] == userId;
                           });
    return (it != users.end()) ? &(*it) : nullptr;
}

// 查找书籍通过ID
json *findBookById(json &books, int bookId)
{
    auto it = std::find_if(books.begin(), books.end(),
                           [bookId](const json &book)
                           {
                               return book.contains("id") && book["id"].is_number_integer() && book["id"] == bookId;
                           });
    return (it != books.end()) ? &(*it) : nullptr;
}

// 查找用户通过用户名
json *findUserByName(json &users, const std::string &name)
{
    auto it = std::find_if(users.begin(), users.end(),
                           [&name](const json &user)
                           {
                               return user.contains("name") && user["name"].is_string() && user["name"] == name;
                           });
    return (it != users.end()) ? &(*it) : nullptr;
}

// 查找书籍通过ISBN
json *findBookByISBN(json &books, const std::string &isbn)
{
    auto it = std::find_if(books.begin(), books.end(),
                           [&isbn](const json &book)
                           {
                               return book.contains("ISBN") && book["ISBN"].is_string() && book["ISBN"] == isbn;
                           });
    return (it != books.end()) ? &(*it) : nullptr;
}

// --- 主应用程序 ---
int main ()
{
    crow::SimpleApp app; // 创建 Crow 应用实例

    // --- 初始加载和 ID 初始化 ---
    {
        std::lock_guard<std::mutex> user_lock(user_mutex);
        std::lock_guard<std::mutex> book_lock(book_mutex);
        json initial_users = loadData(USER_FILE);
        json initial_books = loadData(BOOK_FILE);
        initializeNextUserId(initial_users);
        initializeNextBookId(initial_books);
    }

    // --- 用户管理 API 路由定义 ---

    // GET /api/v1/user - 列出所有用户
    CROW_ROUTE(app, "/api/v1/user").methods(crow::HTTPMethod::Get)([]()
                                                                   {
        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        
        // 移除密码字段以保护隐私
        for (auto& user : users) {
            if (user.contains("password")) {
                user.erase("password");
            }
        }
        
        crow::response res(users.dump());
        res.add_header("Content-Type", "application/json; charset=utf-8");
        return res; });

    // POST /api/v1/user - 创建新用户
    CROW_ROUTE(app, "/api/v1/user").methods(crow::HTTPMethod::Post)([](const crow::request &req)
                                                                    {
        json user_input;
        try {
            user_input = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证输入数据
        if (!validateUserInput(user_input, false)) {
            return crow::response(400, json{{"error", "缺少或无效的必需字段"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);

        // 检查用户名是否已存在
        if (findUserByName(users, user_input["name"])) {
            return crow::response(409, json{{"error", "用户名已存在"}}.dump());
        }

        json new_user;
        new_user["id"] = next_user_id++;
        new_user["name"] = user_input["name"];
        new_user["password"] = user_input["password"];
        new_user["group"] = user_input.value("group", "User");
        new_user["borrowedBooks"] = json::array();
        new_user["borrowedTime"] = json::object();
        new_user["securityAnswer"] = user_input.value("securityAnswer", "");

        users.push_back(new_user);

        if (saveData(users, USER_FILE)) {
            // 返回用户信息时移除密码
            json response_user = new_user;
            //response_user.erase("password");
            
            crow::response res(201, response_user.dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            next_user_id--;
            return crow::response(500, json{{"error", "无法保存新用户到文件"}}.dump());
        } });

    // GET /api/v1/user/{userId} - 获取指定用户信息
    CROW_ROUTE(app, "/api/v1/user/<int>")
        .methods(crow::HTTPMethod::Get)([](int userId)
                                        {
        if (userId <= 0) {
            return crow::response(400, json{{"error", "无效的用户 ID"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        json* user = findUserById(users, userId);

        if (user) {
            json response_user = *user;
            response_user.erase("password"); // 移除密码字段
            
            crow::response res(response_user.dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            return crow::response(404, json{{"error", "用户未找到"}}.dump());
        } });

    // PUT /api/v1/user/{userId} - 更新用户信息
    CROW_ROUTE(app, "/api/v1/user/<int>")
        .methods(crow::HTTPMethod::Put)([](const crow::request &req, int userId)
                                        {
        if (userId <= 0) {
            return crow::response(400, json{{"error", "无效的用户 ID"}}.dump());
        }

        json user_update;
        try {
            user_update = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证输入数据
        if (!validateUserInput(user_update, true)) {
            return crow::response(400, json{{"error", "缺少或无效的必需字段"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        json* user_to_update = findUserById(users, userId);

        if (user_to_update) {
            // 检查用户名是否与其他用户冲突
            if (user_update["name"] != (*user_to_update)["name"]) {
                json* existing_user = findUserByName(users, user_update["name"]);
                if (existing_user && (*existing_user)["id"] != userId) {
                    return crow::response(409, json{{"error", "用户名已被其他用户使用"}}.dump());
                }
            }

            // 更新用户信息
            (*user_to_update)["name"] = user_update["name"];
            if (user_update.contains("password")) {
                (*user_to_update)["password"] = user_update["password"];
            }
            (*user_to_update)["group"] = user_update.value("group", "User");
            if (user_update.contains("securityAnswer")) {
                (*user_to_update)["securityAnswer"] = user_update["securityAnswer"];
            }

            if (saveData(users, USER_FILE)) {
                json response_user = *user_to_update;
                response_user.erase("password");
                
                crow::response res(response_user.dump());
                res.add_header("Content-Type", "application/json; charset=utf-8");
                return res;
            } else {
                return crow::response(500, json{{"error", "无法保存更新后的用户信息"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "用户未找到，无法更新"}}.dump());
        } });

    // DELETE /api/v1/user/{userId} - 删除用户
    CROW_ROUTE(app, "/api/v1/user/<int>")
        .methods(crow::HTTPMethod::Delete)([](int userId)
                                           {
        if (userId <= 0) {
            return crow::response(400, json{{"error", "无效的用户 ID"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        
        auto it = std::find_if(users.begin(), users.end(),
                               [userId](const json& user) {
                                   return user.contains("id") && user["id"].is_number_integer() && user["id"] == userId;
                               });

        if (it != users.end()) {
            users.erase(it);

            if (saveData(users, USER_FILE)) {
                return crow::response(204);
            } else {
                return crow::response(500, json{{"error", "删除用户后无法保存更改"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "用户未找到，无法删除"}}.dump());
        } });

    // POST /api/v1/user/{userId}/reset-password - 重置用户密码
    CROW_ROUTE(app, "/api/v1/user/<int>/reset-password")
        .methods(crow::HTTPMethod::Post)([](int userId)
                                         {
        if (userId <= 0) {
            return crow::response(400, json{{"error", "无效的用户 ID"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        json* user_to_reset = findUserById(users, userId);

        if (user_to_reset) {
            std::string new_password = generateRandomPassword();
            (*user_to_reset)["password"] = new_password;

            if (saveData(users, USER_FILE)) {
                crow::response res(json{{"message", "密码重置成功"}, {"new_password", new_password}}.dump());
                res.add_header("Content-Type", "application/json; charset=utf-8");
                return res;
            } else {
                return crow::response(500, json{{"error", "无法保存重置后的密码"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "用户未找到，无法重置密码"}}.dump());
        } });

    // POST /api/v1/user/{userId}/change-password - 修改用户密码
    CROW_ROUTE(app, "/api/v1/user/<int>/change-password")
        .methods(crow::HTTPMethod::Post)([](const crow::request &req, int userId)
                                         {
        if (userId <= 0) {
            return crow::response(400, json{{"error", "无效的用户 ID"}}.dump());
        }

        json password_change;
        try {
            password_change = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证必需字段
        if (!password_change.contains("old_password") || !password_change["old_password"].is_string() ||
            !password_change.contains("new_password") || !password_change["new_password"].is_string() ||
            password_change["new_password"].get<std::string>().empty()) {
            return crow::response(400, json{{"error", "缺少或无效的密码字段"}}.dump());
        }

        std::lock_guard<std::mutex> lock(user_mutex);
        json users = loadData(USER_FILE);
        json* user_to_change = findUserById(users, userId);

        if (user_to_change) {
            // 验证旧密码
            if ((*user_to_change)["password"] != password_change["old_password"]) {
                return crow::response(401, json{{"error", "旧密码不正确"}}.dump());
            }

            (*user_to_change)["password"] = password_change["new_password"];

            if (saveData(users, USER_FILE)) {
                crow::response res(json{{"message", "密码修改成功"}}.dump());
                res.add_header("Content-Type", "application/json; charset=utf-8");
                return res;
            } else {
                return crow::response(500, json{{"error", "无法保存修改后的密码"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "用户未找到，无法修改密码"}}.dump());
        } });

    // --- 书籍管理 API 路由定义 ---

    // GET /api/v1/book - 列出所有书籍
    CROW_ROUTE(app, "/api/v1/book").methods(crow::HTTPMethod::Get)([]()
                                                                   {
        std::lock_guard<std::mutex> lock(book_mutex);
        json books = loadData(BOOK_FILE);
        
        crow::response res(books.dump());
        res.add_header("Content-Type", "application/json; charset=utf-8");
        return res; });

    // POST /api/v1/book - 创建新书籍
    CROW_ROUTE(app, "/api/v1/book").methods(crow::HTTPMethod::Post)([](const crow::request &req)
                                                                    {
        json book_input;
        try {
            book_input = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证输入数据
        if (!validateBookInput(book_input)) {
            return crow::response(400, json{{"error", "缺少或无效的必需字段"}}.dump());
        }

        std::lock_guard<std::mutex> lock(book_mutex);
        json books = loadData(BOOK_FILE);

        // 检查ISBN是否已存在
        if (findBookByISBN(books, book_input["ISBN"])) {
            return crow::response(409, json{{"error", "ISBN已存在"}}.dump());
        }

        json new_book;
        new_book["id"] = next_book_id++;
        new_book["title"] = book_input["title"];
        new_book["author"] = book_input.value("author", "");
        new_book["publisher"] = book_input.value("publisher", "");
        new_book["publishYear"] = book_input.value("publishYear", 0);
        new_book["ISBN"] = book_input["ISBN"];
        new_book["availableCopies"] = book_input["totalCopies"];
        new_book["totalCopies"] = book_input["totalCopies"];

        books.push_back(new_book);

        if (saveData(books, BOOK_FILE)) {
            crow::response res(201, new_book.dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            next_book_id--;
            return crow::response(500, json{{"error", "无法保存新书籍到文件"}}.dump());
        } });

    // GET /api/v1/book/{bookId} - 获取指定书籍信息
    CROW_ROUTE(app, "/api/v1/book/<int>")
        .methods(crow::HTTPMethod::Get)([](int bookId)
                                        {
        if (bookId <= 0) {
            return crow::response(400, json{{"error", "无效的书籍 ID"}}.dump());
        }

        std::lock_guard<std::mutex> lock(book_mutex);
        json books = loadData(BOOK_FILE);
        json* book = findBookById(books, bookId);

        if (book) {
            crow::response res(book->dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            return crow::response(404, json{{"error", "书籍未找到"}}.dump());
        } });

    // PUT /api/v1/book/{bookId} - 更新书籍信息
    CROW_ROUTE(app, "/api/v1/book/<int>")
        .methods(crow::HTTPMethod::Put)([](const crow::request &req, int bookId)
                                        {
        if (bookId <= 0) {
            return crow::response(400, json{{"error", "无效的书籍 ID"}}.dump());
        }

        json book_update;
        try {
            book_update = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证输入数据
        if (!validateBookInput(book_update)) {
            return crow::response(400, json{{"error", "缺少或无效的必需字段"}}.dump());
        }

        std::lock_guard<std::mutex> lock(book_mutex);
        json books = loadData(BOOK_FILE);
        json* book_to_update = findBookById(books, bookId);

        if (book_to_update) {
            // 检查ISBN是否与其他书籍冲突
            if (book_update["ISBN"] != (*book_to_update)["ISBN"]) {
                json* existing_book = findBookByISBN(books, book_update["ISBN"]);
                if (existing_book && (*existing_book)["id"] != bookId) {
                    return crow::response(409, json{{"error", "ISBN已被其他书籍使用"}}.dump());
                }
            }

            // 计算可用副本数的变化
            int old_total = (*book_to_update)["totalCopies"];
            int new_total = book_update["totalCopies"];
            int old_available = (*book_to_update)["availableCopies"];
            int borrowed = old_total - old_available;
            
            // 确保新的总数不少于已借出的数量
            if (new_total < borrowed) {
                return crow::response(400, json{{"error", "总副本数不能少于已借出的副本数"}}.dump());
            }

            // 更新书籍信息
            (*book_to_update)["title"] = book_update["title"];
            (*book_to_update)["author"] = book_update.value("author", "");
            (*book_to_update)["publisher"] = book_update.value("publisher", "");
            (*book_to_update)["publishYear"] = book_update.value("publishYear", 0);
            (*book_to_update)["ISBN"] = book_update["ISBN"];
            (*book_to_update)["totalCopies"] = new_total;
            (*book_to_update)["availableCopies"] = new_total - borrowed;

            if (saveData(books, BOOK_FILE)) {
                crow::response res(book_to_update->dump());
                res.add_header("Content-Type", "application/json; charset=utf-8");
                return res;
            } else {
                return crow::response(500, json{{"error", "无法保存更新后的书籍信息"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "书籍未找到，无法更新"}}.dump());
        } });

    // DELETE /api/v1/book/{bookId} - 删除书籍
    CROW_ROUTE(app, "/api/v1/book/<int>")
        .methods(crow::HTTPMethod::Delete)([](int bookId)
                                           {
        if (bookId <= 0) {
            return crow::response(400, json{{"error", "无效的书籍 ID"}}.dump());
        }

        std::lock_guard<std::mutex> book_lock(book_mutex);
        std::lock_guard<std::mutex> user_lock(user_mutex);
        
        json books = loadData(BOOK_FILE);
        json users = loadData(USER_FILE);
        
        auto book_it = std::find_if(books.begin(), books.end(),
                                   [bookId](const json& book) {
                                       return book.contains("id") && book["id"].is_number_integer() && book["id"] == bookId;
                                   });

        if (book_it != books.end()) {
            // 检查是否有用户借阅了这本书
            std::string book_isbn = (*book_it)["ISBN"];
            bool is_borrowed = false;
            
            for (const auto& user : users) {
                if (user.contains("borrowedBooks") && user["borrowedBooks"].is_array()) {
                    for (const auto& borrowed_isbn : user["borrowedBooks"]) {
                        if (borrowed_isbn == book_isbn) {
                            is_borrowed = true;
                            break;
                       }
                }    if (is_borrowed) break;
                }
            }
            
            if (is_borrowed) {
                return crow::response(409, json{{"error", "无法删除已被借阅的书籍"}}.dump());
            }

            books.erase(book_it);

            if (saveData(books, BOOK_FILE)) {
                return crow::response(204);
            } else {
                return crow::response(500, json{{"error", "删除书籍后无法保存更改"}}.dump());
            }
        } else {
            return crow::response(404, json{{"error", "书籍未找到，无法删除"}}.dump());
        } });

    // POST /api/v1/book/{bookId}/borrow - 借阅书籍
    CROW_ROUTE(app, "/api/v1/book/<int>/borrow")
        .methods(crow::HTTPMethod::Post)([](const crow::request &req, int bookId)
                                         {
        if (bookId <= 0) {
            return crow::response(400, json{{"error", "无效的书籍 ID"}}.dump());
        }

        json borrow_request;
        try {
            borrow_request = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证必需字段
        if (!borrow_request.contains("userId") || !borrow_request["userId"].is_number_integer()) {
            return crow::response(400, json{{"error", "缺少或无效的用户 ID"}}.dump());
        }

        int userId = borrow_request["userId"];

        std::lock_guard<std::mutex> book_lock(book_mutex);
        std::lock_guard<std::mutex> user_lock(user_mutex);
        
        json books = loadData(BOOK_FILE);
        json users = loadData(USER_FILE);
        
        json* book = findBookById(books, bookId);
        json* user = findUserById(users, userId);

        if (!book) {
            return crow::response(404, json{{"error", "书籍未找到"}}.dump());
        }
        if (!user) {
            return crow::response(404, json{{"error", "用户未找到"}}.dump());
        }

        // 检查书籍是否有可用副本
        if ((*book)["availableCopies"] <= 0) {
            return crow::response(409, json{{"error", "书籍无可用副本"}}.dump());
        }

        // 检查用户是否已经借阅了这本书
        std::string book_isbn = (*book)["ISBN"];
        if (user->contains("borrowedBooks") && (*user)["borrowedBooks"].is_array()) {
            for (const auto& borrowed_isbn : (*user)["borrowedBooks"]) {
                if (borrowed_isbn == book_isbn) {
                    return crow::response(409, json{{"error", "用户已借阅此书"}}.dump());
                }
            }
        }

        // 执行借阅操作
        (*book)["availableCopies"] = (*book)["availableCopies"].get<int>() - 1;
        
        if (!user->contains("borrowedBooks")) {
            (*user)["borrowedBooks"] = json::array();
        }
        if (!user->contains("borrowedTime")) {
            (*user)["borrowedTime"] = json::object();
        }
        
        (*user)["borrowedBooks"].push_back(book_isbn);
        (*user)["borrowedTime"][book_isbn] = getCurrentTimestampISO8601();

        // 保存更改
        bool books_saved = saveData(books, BOOK_FILE);
        bool users_saved = saveData(users, USER_FILE);

        if (books_saved && users_saved) {
            crow::response res(json{{"message", "借阅成功"}, {"borrowTime", (*user)["borrowedTime"][book_isbn]}}.dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            return crow::response(500, json{{"error", "借阅操作失败，无法保存更改"}}.dump());
        } });
        
    // POST /api/v1/book/{bookId}/return - 归还书籍
    CROW_ROUTE(app, "/api/v1/book/<int>/return")
        .methods(crow::HTTPMethod::Post)([](const crow::request &req, int bookId)
                                         {
        if (bookId <= 0) {
            return crow::response(400, json{{"error", "无效的书籍 ID"}}.dump());
        }

        json return_request;
        try {
            return_request = json::parse(req.body);
        } catch (json::parse_error& e) {
            return crow::response(400, json{{"error", "无效的 JSON 格式: " + std::string(e.what())}}.dump());
        }

        // 验证必需字段
        if (!return_request.contains("userId") || !return_request["userId"].is_number_integer()) {
            return crow::response(400, json{{"error", "缺少或无效的用户 ID"}}.dump());
        }

        int userId = return_request["userId"];

        std::lock_guard<std::mutex> book_lock(book_mutex);
        std::lock_guard<std::mutex> user_lock(user_mutex);
        
        json books = loadData(BOOK_FILE);
        json users = loadData(USER_FILE);
        
        json* book = findBookById(books, bookId);
        json* user = findUserById(users, userId);

        if (!book) {
            return crow::response(404, json{{"error", "书籍未找到"}}.dump());
        }
        if (!user) {
            return crow::response(404, json{{"error", "用户未找到"}}.dump());
        }

        // 检查用户是否借阅了这本书
        std::string book_isbn = (*book)["ISBN"];
        bool found_borrowed = false;
        
        if (user->contains("borrowedBooks") && (*user)["borrowedBooks"].is_array()) {
            auto& borrowed_books = (*user)["borrowedBooks"];
            auto it = std::find(borrowed_books.begin(), borrowed_books.end(), book_isbn);
            if (it != borrowed_books.end()) {
                borrowed_books.erase(it);
                found_borrowed = true;
            }
        }

        if (!found_borrowed) {
            return crow::response(409, json{{"error", "用户未借阅此书"}}.dump());
        }

        // 执行归还操作
        (*book)["availableCopies"] = (*book)["availableCopies"].get<int>() + 1;
        
        // 移除借阅时间记录
        if (user->contains("borrowedTime") && (*user)["borrowedTime"].contains(book_isbn)) {
            (*user)["borrowedTime"].erase(book_isbn);
        }

        // 保存更改
        bool books_saved = saveData(books, BOOK_FILE);
        bool users_saved = saveData(users, USER_FILE);

        if (books_saved && users_saved) {
            crow::response res(json{{"message", "归还成功"}}.dump());
            res.add_header("Content-Type", "application/json; charset=utf-8");
            return res;
        } else {
            return crow::response(500, json{{"error", "归还操作失败，无法保存更改"}}.dump());
        } });

    // GET /api/v1/book/search - 搜索书籍
    CROW_ROUTE(app, "/api/v1/book/search")
        .methods(crow::HTTPMethod::Get)([](const crow::request &req)
                                        {
        std::string query = req.url_params.get("q") ? req.url_params.get("q") : "";
        
        if (query.empty()) {
            return crow::response(400, json{{"error", "缺少搜索查询参数 q"}}.dump());
        }

        std::lock_guard<std::mutex> lock(book_mutex);
        json books = loadData(BOOK_FILE);
        json results = json::array();

        // 搜索标题、作者、ISBN中包含查询字符串的书籍
        for (const auto& book : books) {
            std::string title = book.value("title", "");
            std::string author = book.value("author", "");
            std::string isbn = book.value("ISBN", "");
            
            // 转换为小写进行不区分大小写的搜索
            std::transform(title.begin(), title.end(), title.begin(), ::tolower);
            std::transform(author.begin(), author.end(), author.begin(), ::tolower);
            std::transform(isbn.begin(), isbn.end(), isbn.begin(), ::tolower);
            std::string lower_query = query;
            std::transform(lower_query.begin(), lower_query.end(), lower_query.begin(), ::tolower);
            
            if (title.find(lower_query) != std::string::npos ||
                author.find(lower_query) != std::string::npos ||
                isbn.find(lower_query) != std::string::npos) {
                results.push_back(book);
            }
        }

        crow::response res(results.dump());
        res.add_header("Content-Type", "application/json; charset=utf-8");
        return res; });

// --- 运行服务器 ---
// 尝试设置控制台输出为 UTF-8 (仅 Windows 特定，可能不适用于所有终端)
// 这有助于在调试时通过 cout/cerr 打印 UTF-8 字符时不出现乱码。
#ifdef _WIN32
    // "> nul" 用于抑制 chcp 命令本身的输出
    system("chcp 65001 > nul"); // 将控制台代码页设置为 UTF-8
    std::cout << "信息: (Windows) 尝试将控制台代码页设置为 UTF-8 (65001)。" << std::endl;
#endif

    std::cout << "图书管理系统服务器启动中..." << std::endl;
    std::cout << "用户管理 API: /api/v1/user" << std::endl;
    std::cout << "书籍管理 API: /api/v1/book" << std::endl;
    std::cout << "服务器端口: 8080" << std::endl;

    app.port(8080)       // 设置端口号
        .multithreaded() // 启用多线程处理并发请求
        .run();          // 启动服务器并开始监听

    // 如果 run() 成功，程序将在此处阻塞，直到服务器停止
    // run() 之后的代码通常不会执行，除非服务器正常停止
    return 0;
}
